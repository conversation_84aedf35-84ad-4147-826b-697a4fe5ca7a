#!/usr/bin/env python3
"""
Simple OCR Test Script for YOLO2K

This script tests OCR functionality with your configured regions.
"""

import os
import sys

def test_ocr():
    """Test OCR functionality with proper path handling."""
    print("🧪 YOLO2K OCR Test")
    print("=" * 30)
    
    # Ensure we're in the right directory
    if not os.path.exists("F:/YOLO2K"):
        print("❌ Project directory F:/YOLO2K not found!")
        return False
    
    os.chdir("F:/YOLO2K")
    
    # Check if config file exists
    config_path = "F:/YOLO2K/src/config.json"
    if not os.path.exists(config_path):
        print(f"❌ Config file not found at: {config_path}")
        print("Please run region_selector.py first to generate the config file.")
        return False
    
    print(f"✅ Config file found at: {config_path}")
    
    # Test OCR
    try:
        from src.ocr_reader import OCRReader
        
        # Check Tesseract paths
        tesseract_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        ]
        
        tesseract_path = None
        for path in tesseract_paths:
            if os.path.exists(path):
                tesseract_path = path
                break
        
        if not tesseract_path:
            print("❌ Tesseract OCR not found!")
            print("Please install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
            return False
        
        print(f"✅ Tesseract found at: {tesseract_path}")
        
        # Initialize OCR reader
        print("\n🔧 Initializing OCR Reader...")
        reader = OCRReader(config_path, tesseract_path)
        
        print(f"✅ OCR Reader initialized successfully!")
        print(f"📐 Region 1 (Feedback): {reader.region1}")
        print(f"📐 Region 2 (Score/Stats): {reader.region2}")
        
        # Test reading
        print(f"\n📖 Testing OCR Reading (10 seconds)...")
        print("Make sure NBA 2K25 is running with visible text!")
        input("Press Enter to start reading...")
        
        import time
        start_time = time.time()
        readings_found = 0
        
        try:
            total_reward = 0.0
            while time.time() - start_time < 10:
                # Use new reward-based analysis
                result1 = reader.read_text_with_reward(1)
                result2 = reader.read_text_with_reward(2)
                
                if result1['text']:
                    print(f"📝 Region 1: '{result1['text_clean']}'")
                    if result1['reward'] != 0:
                        print(f"   💰 Reward: {result1['reward']:.1f} {result1['matched_terms']}")
                        total_reward += result1['reward']
                    readings_found += 1
                
                if result2['text']:
                    print(f"📊 Region 2: '{result2['text_clean']}'")
                    if result2['reward'] != 0:
                        print(f"   💰 Reward: {result2['reward']:.1f} {result2['matched_terms']}")
                        total_reward += result2['reward']
                    readings_found += 1
                    
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️  Test interrupted by user.")
        finally:
            # Ensure proper cleanup
            reader.cleanup()
            print("🧹 Resources cleaned up.")
        
        print(f"\n✅ OCR Test completed!")
        print(f"📊 Found {readings_found} text readings")
        print(f"💰 Total reward calculated: {total_reward:.1f}")
        
        if readings_found == 0:
            print("\n⚠️  No text was detected. This could mean:")
            print("1. NBA 2K25 is not running or visible")
            print("2. OCR regions don't contain readable text")
            print("3. Text is too small or unclear")
            print("4. Regions need to be adjusted")
            print("\n💡 Try running region_selector.py again to adjust regions")
        else:
            print("🎉 OCR is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        return False

if __name__ == "__main__":
    test_ocr()