#!/usr/bin/env python3
"""
Script to download YOLOv4 weights for the YOLO2K project.
"""

import os
import urllib.request
import sys

def download_file(url, filename):
    """Download a file with progress indication."""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            sys.stdout.write(f"\rDownloading {filename}: {percent}% ({downloaded}/{total_size} bytes)")
            sys.stdout.flush()
    
    print(f"Downloading {filename}...")
    urllib.request.urlretrieve(url, filename, progress_hook)
    print(f"\n{filename} downloaded successfully!")

def main():
    # Create models directory if it doesn't exist
    models_dir = "F:/YOLO2K/models"
    os.makedirs(models_dir, exist_ok=True)
    
    # YOLOv4 weights URL
    yolo_weights_url = "https://github.com/AlexeyAB/darknet/releases/download/darknet_yolo_v3_optimal/yolov4.weights"
    weights_path = os.path.join(models_dir, "yolov4.weights")
    
    if not os.path.exists(weights_path):
        try:
            download_file(yolo_weights_url, weights_path)
        except Exception as e:
            print(f"Error downloading YOLOv4 weights: {e}")
            print("You can manually download from: https://github.com/AlexeyAB/darknet/releases/download/darknet_yolo_v3_optimal/yolov4.weights")
            return False
    else:
        print("YOLOv4 weights already exist.")
    
    print("All model files are ready!")
    return True

if __name__ == "__main__":
    main()