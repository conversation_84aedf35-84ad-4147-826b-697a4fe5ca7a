# YOLO2K Bot Project Notes

## Project Goal
Create a bot that can play NBA 2K. The bot uses computer vision to see the game and a decision-making component to control a virtual gamepad.

## System Architecture

### 1. Vision (The "Eyes")
- **Model:** YOLOv3 (using `yolov3.weights`, `yolov3.cfg`, `coco.names`).
- **Framework:** OpenCV's DNN module.
- **Execution:** Runs on the **CPU** to leave the GPU free.
- **Status:** Successfully detects `'person'` and `'sports ball'` from the game feed.

### 2. Game Feed
- **Method:** High-speed screen capture using the `dxcam` library.
- **Source:** Captures the NBA 2K game being streamed to the PC via the Xbox app.

### 3. Decision Making (The "Brain")
- **Current Logic:** A simple rule-based system in `decision_making.py`.
  - If a 'person' and 'sports ball' are detected, it calculates the vector and returns a `move` command.
  - Otherwise, it returns `do_nothing`.
- **Future Plan:** Evolve this into a neural network that runs on the dedicated **AMD RX 580 GPU**.

### 4. Control (The "Hands")
- **Method:** Controller passthrough system.
- **Libraries:** `vgamepad` for creating a virtual Xbox 360 controller and `inputs` for reading the physical controller.
- **Functionality:**
  - The game sees only the single virtual controller.
  - By default, physical controller inputs are passed through to the virtual one.
  - The bot can override the physical inputs to execute its own commands (e.g., `move`).
- **Driver Requirement:** **ViGEmBus** must be installed on the system.

## Current Status & Next Steps
- The full pipeline (Capture -> Detect -> Decide -> Control) is implemented.
- The user is currently testing the first active bot logic, where the bot should move the player towards the ball.
- The next step is to evaluate the performance of this simple logic and then plan the development of the more advanced neural network for decision-making.
