#!/usr/bin/env python3
"""
Quick Start Script for YOLO2K NBA 2K25 AI Bot

This script provides a simple menu to get you started quickly.
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🚀 {description}")
    print("-" * 50)
    try:
        result = subprocess.run(command, shell=True, check=True, cwd="F:/YOLO2K")
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main_menu():
    """Display the main menu."""
    print("\n🤖 YOLO2K NBA 2K25 AI Bot - Quick Start")
    print("=" * 50)
    print("1. 🔧 Complete Setup (first time)")
    print("2. 📐 Set up screen regions only")
    print("3. 🧪 Test OCR reading")
    print("4. 👁️  Test YOLO detection")
    print("5. 🎮 Run the bot (inference mode)")
    print("6. 🎓 Start training")
    print("7. 📊 Check GPU status")
    print("8. 📥 Download models only")
    print("9. ❓ Help & Documentation")
    print("0. 🚪 Exit")
    print("-" * 50)
    
    choice = input("Enter your choice (0-9): ").strip()
    return choice

def show_help():
    """Show help information."""
    print("\n📚 YOLO2K Help & Documentation")
    print("=" * 40)
    print("\n📖 Quick Guide:")
    print("1. First time? Choose option 1 for complete setup")
    print("2. Have NBA 2K25 running when setting up regions")
    print("3. Test everything before starting training")
    print("4. Training takes time - be patient!")
    print("\n📁 Important Files:")
    print("- src/config.json: Screen regions configuration")
    print("- src/train.py: Training script with reward system")
    print("- src/main.py: Run the trained bot")
    print("- NBA2K25_SETUP_GUIDE.md: Detailed setup guide")
    print("\n🔗 Useful Commands:")
    print("- python src/region_selector.py: Set up screen regions")
    print("- python src/ocr_reader.py: Test OCR reading")
    print("- python src/main.py: Run the bot")
    print("- python src/train.py: Start training")

def main():
    """Main function."""
    # Check if we're in the right directory
    if not os.path.exists("F:/YOLO2K"):
        print("❌ Project directory F:/YOLO2K not found!")
        print("Please make sure you're running this from the correct location.")
        return
    
    os.chdir("F:/YOLO2K")
    
    while True:
        choice = main_menu()
        
        if choice == "1":
            # Complete setup
            if run_command("python setup_nba2k25.py", "Complete Setup"):
                print("\n🎉 Setup complete! You can now run the bot or start training.")
        
        elif choice == "2":
            # Set up regions only
            print("\n📐 Choose Region Selector:")
            print("1. Live Region Selector (Recommended)")
            print("2. Standard Region Selector")
            selector_choice = input("Enter choice (1 or 2): ").strip()
            
            if selector_choice == "1":
                run_command("python src/region_selector_live.py", "Live Screen Region Setup")
            else:
                run_command("python src/region_selector.py", "Standard Screen Region Setup")
        
        elif choice == "3":
            # Test OCR
            if os.path.exists("src/config.json"):
                run_command("python test_ocr.py", "OCR Test")
            else:
                print("❌ No config file found. Set up screen regions first (option 2).")
        
        elif choice == "4":
            # Test YOLO detection
            if os.path.exists("models/yolov4.weights"):
                print("\n👁️  Testing YOLO Detection")
                print("This will open a window showing detected objects.")
                print("Make sure NBA 2K25 is running!")
                input("Press Enter to continue...")
                run_command("python src/main.py", "YOLO Detection Test")
            else:
                print("❌ YOLO weights not found. Run complete setup first (option 1).")
        
        elif choice == "5":
            # Run the bot
            if os.path.exists("src/config.json") and os.path.exists("models/yolov4.weights"):
                print("\n🎮 Starting the bot in inference mode")
                print("Make sure NBA 2K25 is running!")
                print("Press Ctrl+C to stop the bot.")
                input("Press Enter to start...")
                run_command("python src/main.py", "Bot Execution")
            else:
                print("❌ Missing configuration or models. Run complete setup first (option 1).")
        
        elif choice == "6":
            # Start training
            if os.path.exists("src/config.json") and os.path.exists("models/yolov4.weights"):
                print("\n🎓 Starting AI Training")
                print("This will take a long time! Make sure:")
                print("- NBA 2K25 is running")
                print("- You're in a game mode suitable for training")
                print("- Your computer won't go to sleep")
                print("Press Ctrl+C to stop training and save the model.")
                input("Press Enter to start training...")
                run_command("python src/train.py", "AI Training")
            else:
                print("❌ Missing configuration or models. Run complete setup first (option 1).")
        
        elif choice == "7":
            # Check GPU status
            run_command("python -c \"from src.gpu_optimizer import setup_amd_gpu, monitor_gpu_usage; setup_amd_gpu(); monitor_gpu_usage()\"", "GPU Status Check")
        
        elif choice == "8":
            # Download models only
            run_command("python download_models.py", "Model Download")
        
        elif choice == "9":
            # Show help
            show_help()
        
        elif choice == "0":
            # Exit
            print("\n👋 Thanks for using YOLO2K! Good luck with your AI training!")
            break
        
        else:
            print("❌ Invalid choice. Please enter a number from 0-9.")
        
        # Wait for user input before showing menu again
        if choice != "0" and choice != "9":
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()