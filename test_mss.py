#!/usr/bin/env python3
"""Quick test to check if MSS is available"""

try:
    import mss
    print("✅ MSS is available")
    
    # Test basic functionality
    with mss.mss() as sct:
        print(f"✅ MSS initialized successfully")
        print(f"📺 Monitors detected: {len(sct.monitors)}")
        for i, monitor in enumerate(sct.monitors):
            print(f"   Monitor {i}: {monitor}")
            
except ImportError:
    print("❌ MSS not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "mss"])
    print("✅ MSS installed successfully")
    
except Exception as e:
    print(f"❌ Error with MSS: {e}")