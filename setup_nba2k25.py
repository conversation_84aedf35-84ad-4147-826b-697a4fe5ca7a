#!/usr/bin/env python3
"""
NBA 2K25 Setup Script for YOLO2K AI Bot

This script helps you:
1. Download required model files
2. Set up screen regions for NBA 2K25
3. Test OCR and object detection
4. Verify GPU configuration
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if all required packages are installed."""
    required_packages = [
        'torch', 'torchvision', 'opencv-python', 'pytesseract', 
        'dxcam', 'numpy', 'pillow', 'mss'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\nInstall them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_tesseract():
    """Check if Tesseract OCR is installed."""
    tesseract_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', ''))
    ]
    
    for path in tesseract_paths:
        if os.path.exists(path):
            print(f"✅ Tesseract found at: {path}")
            return path
    
    print("❌ Tesseract OCR not found!")
    print("Download and install from: https://github.com/UB-Mannheim/tesseract/wiki")
    return None

def download_yolo_models():
    """Download YOLOv4 weights if not present."""
    models_dir = Path("F:/YOLO2K/models")
    weights_path = models_dir / "yolov4.weights"
    
    if weights_path.exists():
        print("✅ YOLOv4 weights already exist")
        return True
    
    print("📥 Downloading YOLOv4 weights (245MB)...")
    try:
        import urllib.request
        url = "https://github.com/AlexeyAB/darknet/releases/download/darknet_yolo_v3_optimal/yolov4.weights"
        urllib.request.urlretrieve(url, str(weights_path))
        print("✅ YOLOv4 weights downloaded successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to download YOLOv4 weights: {e}")
        print("Please download manually from the URL above")
        return False

def setup_nba2k25_regions():
    """Interactive setup for NBA 2K25 screen regions."""
    print("\n🏀 NBA 2K25 Region Setup")
    print("=" * 50)
    
    print("Choose your region selector:")
    print("1. 🔴 Live Region Selector (Recommended for NBA 2K25)")
    print("   - Live screen updates while selecting")
    print("   - NBA 2K25 specific guidance")
    print("   - Preview of selected regions")
    print("2. 📷 Standard Region Selector")
    print("   - Single screenshot approach")
    print("   - Manual refresh when needed")
    
    choice = input("\nEnter choice (1 or 2): ").strip()
    
    print("\nThis will help you set up screen regions for NBA 2K25.")
    print("You'll need to:")
    print("1. Have NBA 2K25 running in windowed or fullscreen mode")
    print("2. Select the main game area for YOLO detection")
    print("3. Select feedback text areas for OCR")
    
    input("\nPress Enter when NBA 2K25 is running and ready...")
    
    # Run the appropriate region selector
    try:
        if choice == "1":
            from src.region_selector_live import main as run_live_selector
            run_live_selector()
        else:
            from src.region_selector import main as run_region_selector
            run_region_selector()
        return True
    except Exception as e:
        print(f"❌ Error running region selector: {e}")
        print("Make sure DXCam is installed: pip install dxcam")
        return False

def test_configuration():
    """Test the complete configuration."""
    print("\n🧪 Testing Configuration")
    print("=" * 30)
    
    config_path = "F:/YOLO2K/src/config.json"
    
    if not os.path.exists(config_path):
        print("❌ Config file not found. Run region setup first.")
        return False
    
    # Test OCR
    print("Testing OCR...")
    try:
        from src.ocr_reader import OCRReader
        tesseract_path = check_tesseract()
        if tesseract_path:
            ocr_reader = OCRReader(config_path, tesseract_path)
            print("✅ OCR reader initialized successfully")
        else:
            print("❌ OCR test failed - Tesseract not found")
            return False
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        return False
    
    # Test YOLO detection
    print("Testing YOLO detection...")
    try:
        from src.detection import ObjectDetector
        detector = ObjectDetector(
            config_path,
            "F:/YOLO2K/models/yolov4.weights",
            "F:/YOLO2K/models/yolov4.cfg",
            "F:/YOLO2K/models/coco.names"
        )
        print("✅ YOLO detector initialized successfully")
    except Exception as e:
        print(f"❌ YOLO test failed: {e}")
        return False
    
    # Test GPU
    print("Testing GPU configuration...")
    try:
        from src.gpu_optimizer import setup_amd_gpu
        device = setup_amd_gpu()
        print(f"✅ GPU test completed - Using: {device}")
    except Exception as e:
        print(f"❌ GPU test failed: {e}")
        return False
    
    print("\n🎉 All tests passed! Your setup is ready.")
    return True

def create_nba2k25_config():
    """Create a sample configuration for NBA 2K25."""
    sample_config = {
        "main_capture_region": [100, 100, 1820, 980],  # Adjust for your screen
        "ocr_region_1": [50, 50, 400, 150],  # Feedback text area
        "ocr_region_2": [1520, 50, 1870, 150]  # Score/stats area
    }
    
    config_path = "F:/YOLO2K/src/config_sample.json"
    with open(config_path, 'w') as f:
        json.dump(sample_config, f, indent=4)
    
    print(f"📝 Sample config created at: {config_path}")
    print("Use this as a reference for your screen regions.")

def main():
    """Main setup function."""
    print("🤖 YOLO2K NBA 2K25 Setup")
    print("=" * 40)
    
    # Check project directory
    if not os.path.exists("F:/YOLO2K"):
        print("❌ Project directory F:/YOLO2K not found!")
        return
    
    os.chdir("F:/YOLO2K")
    
    # Step 1: Check dependencies
    print("\n1️⃣ Checking Dependencies...")
    if not check_dependencies():
        return
    
    # Step 2: Check Tesseract
    print("\n2️⃣ Checking Tesseract OCR...")
    tesseract_path = check_tesseract()
    if not tesseract_path:
        return
    
    # Step 3: Download models
    print("\n3️⃣ Checking YOLO Models...")
    if not download_yolo_models():
        return
    
    # Step 4: Create sample config
    print("\n4️⃣ Creating Sample Configuration...")
    create_nba2k25_config()
    
    # Step 5: Interactive setup
    print("\n5️⃣ Screen Region Setup")
    setup_choice = input("Do you want to set up screen regions now? (y/n): ").lower()
    if setup_choice == 'y':
        if setup_nba2k25_regions():
            print("✅ Region setup completed")
        else:
            print("❌ Region setup failed")
            return
    
    # Step 6: Test configuration
    if os.path.exists("F:/YOLO2K/src/config.json"):
        print("\n6️⃣ Testing Configuration...")
        test_configuration()
    else:
        print("\n⚠️ Skipping tests - no config file found")
        print("Run region setup to create config.json")
    
    print("\n🎯 Setup Complete!")
    print("\nNext steps:")
    print("1. Run 'python src/main.py' to test the bot")
    print("2. Run 'python src/train.py' to start training")
    print("3. Adjust reward keywords in train.py for NBA 2K25")

if __name__ == "__main__":
    main()