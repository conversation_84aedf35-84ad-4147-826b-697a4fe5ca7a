import os
import time
import cv2
import numpy as np
from detection import ObjectDetector
from input_simulation import InputSimulator
from decision_ai_tf_v1 import DecisionAI_TF, Action, monitor_gpu_usage
from ocr_reader import OCRReader


def main():
    # Get the absolute path to the project's root directory
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

    # Construct absolute paths for the models and config
    config_file_path = os.path.join(project_root, 'src', 'config.json')
    model_config_path = os.path.join(project_root, 'models', 'yolov4.cfg')
    model_path = os.path.join(project_root, 'models', 'yolov4.weights')
    labels_path = os.path.join(project_root, 'models', 'coco.names')
    tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # Update this path if needed
    
    # Path to your trained TensorFlow model
    decision_model_path = os.path.join(project_root, 'models', 'decision_ai_tf_amd.h5')

    print("🎯 Initializing YOLO2K with TensorFlow DirectML (AMD RX 580)")
    print("=" * 60)

    # Check if config file exists
    if not os.path.exists(config_file_path):
        print(f"❌ Config file not found at {config_file_path}")
        print("Please run region_selector.py first to set up screen regions.")
        return

    try:
        # Initialize YOLO detector (CPU)
        print("\n🔍 Initializing YOLO Detector (CPU)...")
        detector = ObjectDetector(
            config_file_path=config_file_path,
            model_path=model_path,
            config_path=model_config_path,
            labels_path=labels_path
        )
        print("✅ YOLO Detector initialized on CPU")

        # Initialize OCR reader
        print("\n📝 Initializing OCR Reader...")
        ocr_reader = OCRReader(config_file_path, tesseract_path)
        print("✅ OCR Reader initialized")

        # Initialize input simulator
        print("\n🎮 Initializing Input Simulator...")
        input_sim = InputSimulator()
        print("✅ Input Simulator initialized")

        # Initialize TensorFlow Decision AI (AMD GPU)
        print("\n🧠 Initializing Decision AI (AMD GPU via DirectML)...")
        num_actions = len(Action)
        decision_ai = DecisionAI_TF.create_optimized_model(
            num_actions=num_actions,
            input_size=100
        )

        # Load trained model if it exists
        if os.path.exists(decision_model_path):
            decision_ai.load_model(decision_model_path)
            print(f"✅ Loaded trained model from {decision_model_path}")
        else:
            print("⚠️  No trained model found. Using random initialization.")
            print(f"💡 Train a model first using: python src/train_tf.py")

        # Monitor GPU status
        print("\n📊 GPU Status:")
        monitor_gpu_usage()

    except Exception as e:
        print(f"❌ Error initializing components: {e}")
        import traceback
        traceback.print_exc()
        return

    print("\n🚀 YOLO2K AI Bot Starting...")
    print("🎮 Bot running with TensorFlow DirectML on AMD RX 580")
    print("📝 OCR feedback enabled")
    print("⏹️  Press Ctrl+C to quit or 'q' in vision window")
    print("=" * 60)

    frame_count = 0
    action_count = 0

    try:
        while True:
            frame_count += 1

            # Capture and detect objects using YOLO (CPU)
            detected_objects, frame = detector.capture_and_detect(
                conf_threshold=0.25, nms_threshold=0.4
            )

            if frame is None:
                time.sleep(0.1)
                continue

            # Draw bounding boxes on the frame for visualization
            for obj in detected_objects:
                box = obj['box']
                x, y, w, h = box[0], box[1], box[2], box[3]
                label = f"{obj['label']}: {obj['confidence']:.2f}"
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # Read OCR feedback with enhanced analysis
            ocr_result_1 = ocr_reader.read_text_with_reward(1)  # Primary feedback
            ocr_result_2 = ocr_reader.read_text_with_reward(2)  # Secondary feedback

            # Display OCR text and rewards on frame
            if ocr_result_1['text']:
                ocr_text = ocr_result_1['text_clean'][:25]
                reward = ocr_result_1['reward']
                color = (0, 255, 0) if reward > 0 else (0, 0, 255) if reward < 0 else (0, 255, 255)
                cv2.putText(frame, f"OCR1: {ocr_text} ({reward:+.1f})", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            if ocr_result_2['text']:
                ocr_text = ocr_result_2['text_clean'][:25]
                reward = ocr_result_2['reward']
                color = (0, 255, 0) if reward > 0 else (0, 0, 255) if reward < 0 else (0, 255, 255)
                cv2.putText(frame, f"OCR2: {ocr_text} ({reward:+.1f})", (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Add frame counter and GPU indicator
            cv2.putText(frame, f"Frame: {frame_count} | GPU: AMD RX 580", (10, frame.shape[0] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            # Display the frame
            cv2.imshow("YOLO2K Vision (TensorFlow DirectML)", frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

            # Prepare state for TensorFlow Decision AI
            state = DecisionAI_TF.preprocess_input(detected_objects)

            # Make decisions using TensorFlow model (AMD GPU)
            try:
                action_index = decision_ai.select_action(state)
                action_enum = Action(action_index)
                action_count += 1

                # Execute the command
                input_sim.execute_bot_command(action_enum)

                # Print feedback for debugging (every 10 frames to reduce spam)
                if frame_count % 10 == 0:
                    total_reward = ocr_result_1['reward'] + ocr_result_2['reward']
                    print(f"Frame {frame_count}: Objects={len(detected_objects)}, "
                          f"Action={action_enum.name}, Reward={total_reward:+.2f}")

            except Exception as e:
                print(f"⚠️  Action selection error: {e}")
                # Continue without taking action

            # Control loop frequency
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n⏹️  Ctrl+C received. Shutting down...")
    except Exception as e:
        print(f"❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up resources
        print("\n🧹 Cleaning up resources...")
        try:
            input_sim.shutdown()
            ocr_reader.cleanup()
            cv2.destroyAllWindows()
        except:
            pass

        print("📊 Session Summary:")
        print(f"  - Total frames processed: {frame_count}")
        print(f"  - Total actions taken: {action_count}")
        print(f"  - Neural Network: TensorFlow DirectML (AMD RX 580)")
        print(f"  - Object Detection: YOLO (CPU)")
        print("✅ YOLO2K bot has stopped.")


if __name__ == "__main__":
    main()
