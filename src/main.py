import os
import torch
from detection import ObjectDetector
from input_simulation import InputSimulator
from decision_ai import DecisionAI, Action
from ocr_reader import OCRReader
import time
import cv2


def main():
    # Get the absolute path to the project's root directory
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

    # Construct absolute paths for the models and config
    config_file_path = os.path.join(project_root, 'src', 'config.json')
    model_config_path = os.path.join(project_root, 'models', 'yolov4.cfg')
    model_path = os.path.join(project_root, 'models', 'yolov4.weights')
    labels_path = os.path.join(project_root, 'models', 'coco.names')
    tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # Update this path if needed
    
    # Path to your trained decision-making model
    decision_model_path = os.path.join(project_root, 'models', 'decision_model.pth')

    print("Initializing YOLO2K components...")
    
    # Check if config file exists
    if not os.path.exists(config_file_path):
        print(f"ERROR: Config file not found at {config_file_path}")
        print("Please run region_selector.py first to set up screen regions.")
        return

    try:
        # Initialize the components with proper parameters
        detector = ObjectDetector(
            config_file_path=config_file_path,
            model_path=model_path,
            config_path=model_config_path,
            labels_path=labels_path
        )
        
        # Initialize OCR reader
        ocr_reader = OCRReader(config_file_path, tesseract_path)
        
        input_sim = InputSimulator()
        
        # Initialize the Decision AI with proper device support
        num_actions = len(Action)
        decision_ai = DecisionAI(num_actions=num_actions)
        
        # Set up device (prioritize AMD GPU if available)
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        decision_ai.to(device)
        
        # Load trained model if it exists
        if os.path.exists(decision_model_path):
            decision_ai.load_state_dict(torch.load(decision_model_path, map_location=device))
            print(f"Loaded trained model from {decision_model_path}")
        else:
            print("No trained model found. Using random initialization.")
            
        print(f"Using device: {device}")
        
    except Exception as e:
        print(f"Error initializing components: {e}")
        return

    print("YOLO2K bot starting...")
    print("Bot is running with OCR feedback. Press Ctrl+C in the terminal to quit.")
    print("Press 'q' in the vision window to quit.")

    try:
        while True:
            # Capture and detect objects using the integrated detector
            detected_objects, frame = detector.capture_and_detect(
                conf_threshold=0.25, nms_threshold=0.4
            )

            if frame is None:
                time.sleep(0.1)
                continue

            # Draw bounding boxes on the frame for visualization
            for obj in detected_objects:
                box = obj['box']
                x, y, w, h = box[0], box[1], box[2], box[3]
                label = f"{obj['label']}: {obj['confidence']:.2f}"
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # Read OCR feedback from the game
            ocr_text_1 = ocr_reader.read_text_from_region(1)  # Primary feedback region
            ocr_text_2 = ocr_reader.read_text_from_region(2)  # Secondary feedback region
            
            # Display OCR text on the frame
            if ocr_text_1:
                cv2.putText(frame, f"OCR1: {ocr_text_1[:30]}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            if ocr_text_2:
                cv2.putText(frame, f"OCR2: {ocr_text_2[:30]}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

            # Display the frame
            cv2.imshow("YOLO2K Vision", frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

            # Prepare state for decision AI
            state_tensor = DecisionAI.preprocess_input(detected_objects).to(device)
            
            # Make decisions based on detected objects
            with torch.no_grad():
                action_index = decision_ai.select_action(state_tensor.unsqueeze(0))
                action_enum = Action(action_index)
            
            # Execute the command from the bot
            input_sim.execute_bot_command(action_enum)
            
            # Print feedback for debugging
            if ocr_text_1 or ocr_text_2:
                print(f"OCR Feedback - Region 1: '{ocr_text_1}', Region 2: '{ocr_text_2}', Action: {action_enum.name}")

            # Add a small delay to control the loop frequency
            time.sleep(0.1)

    except KeyboardInterrupt:
        print("\nCtrl+C received. Shutting down...")
    except Exception as e:
        print(f"Error during execution: {e}")
    finally:
        # Clean up resources
        input_sim.shutdown()
        cv2.destroyAllWindows()
        print("YOLO2K bot has stopped.")


if __name__ == "__main__":
    main()
