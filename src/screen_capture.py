import numpy as np
import dxcam

class ScreenCapture:
    """
    <PERSON><PERSON> capturing the screen using DXCAM.
    """
    def __init__(self, monitor_number=0):
        """
        Initializes the screen capture.

        :param monitor_number: The monitor to capture. DXCAM is 0-indexed.
        """
        self.camera = dxcam.create(output_idx=monitor_number, output_color="BGR")
        if self.camera is None:
            raise RuntimeError("Failed to initialize DXCAM.")
        
        self.width = self.camera.width
        self.height = self.camera.height

    def capture_frame(self):
        """
        Captures a frame from the screen.

        :return: A numpy array representing the captured frame in BGR format.
        """
        return self.camera.grab()

    def close(self):
        """
        Releases the DXCAM resources.
        """
        del self.camera
