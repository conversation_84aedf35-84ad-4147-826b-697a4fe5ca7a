#!/usr/bin/env python3
"""
Enhanced Region Selector with Live Preview for NBA 2K25

This version provides:
- Live screen updates while selecting regions
- Better visual feedback
- NBA 2K25 specific guidance
- Preview of selected regions
"""

import cv2
import json
import dxcam
import numpy as np
import time
import threading

class LiveRegionSelector:
    def __init__(self):
        self.camera = None
        self.current_image = None
        self.ref_point = []
        self.cropping = False
        self.live_update = True
        self.update_thread = None
        
        self.regions = {
            "main_capture_region": None,
            "ocr_region_1": None,
            "ocr_region_2": None
        }
        
        self.region_names = {
            "main_capture_region": "Main YOLOv4 Capture Region (Game Court Area)",
            "ocr_region_1": "First OCR Region (Game Feedback Text)",
            "ocr_region_2": "Second OCR Region (Score/Stats Area)"
        }
        
        self.nba2k25_tips = {
            "main_capture_region": [
                "📍 Select the main basketball court area",
                "✅ Include: Players, ball, court lines",
                "❌ Exclude: UI overlays, menus, advertisements",
                "💡 Tip: Focus on the central playing area"
            ],
            "ocr_region_1": [
                "📍 Select where game feedback appears",
                "✅ Look for: 'Good Shot', 'Turnover', 'Assist' text",
                "❌ Avoid: Moving elements, player names",
                "💡 Tip: Usually center-top or center-bottom of screen"
            ],
            "ocr_region_2": [
                "📍 Select scoreboard or stats area",
                "✅ Include: Score, time, player stats",
                "❌ Avoid: Animated elements",
                "💡 Tip: Usually top corners of the screen"
            ]
        }

    def initialize_camera(self):
        """Initialize DXCam with error handling."""
        try:
            self.camera = dxcam.create()
            frame = self.camera.grab()
            if frame is not None:
                self.current_image = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                print(f"✅ DXCam initialized - Screen: {self.current_image.shape}")
                return True
            else:
                print("❌ Failed to capture initial frame")
                return False
        except Exception as e:
            print(f"❌ DXCam initialization failed: {e}")
            return False

    def update_screen_live(self):
        """Continuously update the screen capture in a separate thread."""
        while self.live_update and self.camera:
            try:
                frame = self.camera.grab()
                if frame is not None:
                    self.current_image = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                time.sleep(0.1)  # Update 10 times per second
            except Exception as e:
                print(f"Live update error: {e}")
                break

    def click_and_crop(self, event, x, y, flags, param):
        """Enhanced mouse callback with better visual feedback."""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.ref_point = [(x, y)]
            self.cropping = True

        elif event == cv2.EVENT_MOUSEMOVE and self.cropping:
            # Show live rectangle while dragging
            temp_image = self.current_image.copy()
            if len(self.ref_point) == 1:
                cv2.rectangle(temp_image, self.ref_point[0], (x, y), (0, 255, 255), 2)
                cv2.imshow("Region Selector", temp_image)

        elif event == cv2.EVENT_LBUTTONUP:
            self.ref_point.append((x, y))
            self.cropping = False

    def draw_current_selection(self, image):
        """Draw the current selection on the image."""
        if len(self.ref_point) == 2:
            cv2.rectangle(image, self.ref_point[0], self.ref_point[1], (0, 255, 0), 3)
            # Add text showing dimensions
            x1, y1 = self.ref_point[0]
            x2, y2 = self.ref_point[1]
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            cv2.putText(image, f"Size: {width}x{height}", 
                       (min(x1, x2), min(y1, y2) - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        return image

    def show_tips(self, region_key):
        """Display NBA 2K25 specific tips for the current region."""
        print(f"\n💡 NBA 2K25 Tips for {self.region_names[region_key]}:")
        for tip in self.nba2k25_tips[region_key]:
            print(f"   {tip}")

    def select_region(self, region_key):
        """Select a specific region with live preview."""
        region_name = self.region_names[region_key]
        self.ref_point = []
        
        print(f"\n🎯 Selecting: {region_name}")
        self.show_tips(region_key)
        
        print(f"\nControls:")
        print("  🖱️  Click and drag to select region")
        print("  ✅ Press 'c' to confirm selection")
        print("  🔄 Press 'r' to reset selection")
        print("  🔄 Press 'f' to refresh screen")
        print("  ⏸️  Press 's' to stop/start live updates")
        print("  ❌ Press 'q' to quit")

        cv2.namedWindow("Region Selector", cv2.WINDOW_NORMAL)
        cv2.setMouseCallback("Region Selector", self.click_and_crop)

        while True:
            # Create display image
            display_image = self.current_image.copy()
            display_image = self.draw_current_selection(display_image)
            
            # Add instruction text
            cv2.putText(display_image, f"Selecting: {region_name}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(display_image, "c=confirm, r=reset, f=refresh, s=toggle live", 
                       (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            cv2.imshow("Region Selector", display_image)
            key = cv2.waitKey(30) & 0xFF

            if key == ord("c"):  # Confirm
                if len(self.ref_point) == 2:
                    x1, y1 = self.ref_point[0]
                    x2, y2 = self.ref_point[1]
                    region = (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2))
                    self.regions[region_key] = region
                    print(f"✅ Region saved: {region}")
                    
                    # Show preview of selected region
                    self.preview_region(region, region_name)
                    return True
                else:
                    print("❌ No region selected. Please draw a rectangle first.")

            elif key == ord("r"):  # Reset
                self.ref_point = []
                print("🔄 Selection reset")

            elif key == ord("f"):  # Force refresh
                try:
                    frame = self.camera.grab()
                    if frame is not None:
                        self.current_image = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        print("🔄 Screen refreshed")
                except Exception as e:
                    print(f"❌ Refresh failed: {e}")

            elif key == ord("s"):  # Toggle live updates
                self.live_update = not self.live_update
                if self.live_update and not self.update_thread.is_alive():
                    self.update_thread = threading.Thread(target=self.update_screen_live)
                    self.update_thread.daemon = True
                    self.update_thread.start()
                print(f"🔄 Live updates: {'ON' if self.live_update else 'OFF'}")

            elif key == ord("q"):  # Quit
                return False

    def preview_region(self, region, region_name):
        """Show a preview of the selected region."""
        x1, y1, x2, y2 = region
        cropped = self.current_image[y1:y2, x1:x2]
        
        if cropped.size > 0:
            # Resize for better viewing if too small
            height, width = cropped.shape[:2]
            if width < 200 or height < 100:
                scale = max(200/width, 100/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                cropped = cv2.resize(cropped, (new_width, new_height))
            
            cv2.imshow(f"Preview: {region_name}", cropped)
            print(f"👀 Preview shown for {region_name}")
            print("Press any key in the preview window to continue...")
            cv2.waitKey(0)
            cv2.destroyWindow(f"Preview: {region_name}")

    def run(self):
        """Main execution function."""
        print("🖥️  YOLO2K Live Region Selector for NBA 2K25")
        print("=" * 60)
        
        if not self.initialize_camera():
            return False

        # Start live update thread
        self.update_thread = threading.Thread(target=self.update_screen_live)
        self.update_thread.daemon = True
        self.update_thread.start()

        print("\n🏀 Make sure NBA 2K25 is running and visible!")
        input("Press Enter when ready...")

        # Select each region
        for region_key in self.regions.keys():
            if not self.select_region(region_key):
                print("❌ Region selection cancelled")
                self.cleanup()
                return False

        # Stop live updates
        self.live_update = False
        cv2.destroyAllWindows()

        # Save configuration
        return self.save_config()

    def save_config(self):
        """Save the configuration to file."""
        config_path = 'F:/YOLO2K/src/config.json'
        
        # Validate all regions are set
        missing = [k for k, v in self.regions.items() if v is None]
        if missing:
            print(f"❌ Missing regions: {missing}")
            return False

        try:
            with open(config_path, 'w') as f:
                json.dump(self.regions, f, indent=4)
            
            print(f"\n✅ Configuration saved to {config_path}")
            print("\n📋 Region Summary:")
            for key, value in self.regions.items():
                print(f"  {key}: {value}")
            
            print(f"\n🎯 Next Steps:")
            print("1. Test OCR: python src/ocr_reader.py")
            print("2. Test detection: python src/main.py")
            print("3. Start training: python src/train.py")
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving config: {e}")
            return False

    def cleanup(self):
        """Clean up resources."""
        self.live_update = False
        if self.camera:
            self.camera.release()
        cv2.destroyAllWindows()

def main():
    selector = LiveRegionSelector()
    try:
        success = selector.run()
        if success:
            print("\n🎉 Region selection completed successfully!")
        else:
            print("\n❌ Region selection failed or was cancelled")
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
    finally:
        selector.cleanup()

if __name__ == "__main__":
    main()