#!/usr/bin/env python3
"""
Enhanced Training script using TensorFlow with DirectML for AMD RX 580
YOLO runs on CPU, Neural Network runs on AMD GPU
Integrated with improved OCR reward system
"""

import tensorflow as tf
import numpy as np
import json
import time
import os
from decision_ai_tf_v1 import DecisionAI_TF, Action, monitor_gpu_usage
from detection import ObjectDetector
from ocr_reader import <PERSON><PERSON>Reader
from input_simulation import InputSimulator

class EnhancedRewardSystem:
    """
    Enhanced reward system that integrates OCR feedback with gameplay
    """
    def __init__(self, ocr_reader):
        self.ocr_reader = ocr_reader
        self.time_penalty = -0.005
        self.reward_history = []
        self.max_history = 100

    def get_reward(self):
        """Calculate reward from OCR feedback"""
        total_reward = self.time_penalty

        # Get OCR analysis from both regions
        result1 = self.ocr_reader.read_text_with_reward(1)
        result2 = self.ocr_reader.read_text_with_reward(2)

        # Process primary feedback (region 1)
        if result1['text'] and result1.get('confidence', 0) >= 2.0:
            reward1 = result1['reward']
            total_reward += reward1

            if reward1 != 0:
                print(f"  OCR1 Reward: {reward1:+.2f} - '{result1['text_clean']}'")

        # Process secondary feedback (region 2) with lower weight
        if result2['text'] and result2.get('confidence', 0) >= 2.0:
            reward2 = result2['reward'] * 0.5  # Lower weight
            total_reward += reward2

            if reward2 != 0:
                print(f"  OCR2 Reward: {reward2:+.2f} - '{result2['text_clean']}'")

        # Track history
        self.reward_history.append(total_reward)
        if len(self.reward_history) > self.max_history:
            self.reward_history.pop(0)

        return total_reward

    def get_stats(self):
        """Get reward statistics"""
        if not self.reward_history:
            return {"avg": 0, "min": 0, "max": 0, "count": 0}

        return {
            "avg": np.mean(self.reward_history),
            "min": np.min(self.reward_history),
            "max": np.max(self.reward_history),
            "count": len(self.reward_history)
        }

def load_config():
    """Load training configuration"""
    config_path = "F:/YOLO2K/src/config.json"
    if not os.path.exists(config_path):
        print("❌ Config file not found. Run region_selector.py first.")
        return None

    with open(config_path, 'r') as f:
        return json.load(f)

def simulate_training_data(detector, num_samples=100):
    """
    Generate simulated training data
    In a real scenario, this would come from actual gameplay
    """
    print(f"🎯 Generating {num_samples} training samples...")
    
    states = []
    actions = []
    rewards = []
    
    for i in range(num_samples):
        # Simulate object detection
        detected_objects = [
            {'class_id': np.random.randint(0, 5), 
             'box': [np.random.randint(0, 800), np.random.randint(0, 600), 
                    np.random.randint(20, 100), np.random.randint(20, 100)]},
            {'class_id': np.random.randint(0, 5), 
             'box': [np.random.randint(0, 800), np.random.randint(0, 600), 
                    np.random.randint(20, 100), np.random.randint(20, 100)]}
        ]
        
        # Preprocess state
        state = DecisionAI_TF.preprocess_input(detected_objects)
        
        # Convert to numpy if it's a tensor, otherwise use as-is
        if hasattr(state, 'numpy') and callable(getattr(state, 'numpy')):
            states.append(state.numpy())
        elif isinstance(state, np.ndarray):
            states.append(state)
        else:
            states.append(np.array(state))
        
        # Random action and reward
        actions.append(np.random.randint(0, len(Action)))
        rewards.append(np.random.uniform(-1, 1))
        
        if (i + 1) % 20 == 0:
            print(f"  Generated {i + 1}/{num_samples} samples...")
    
    return np.array(states), np.array(actions), np.array(rewards)

def train_model():
    """Main training function"""
    print("🎯 Starting TensorFlow DirectML Training for AMD RX 580")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    if config is None:
        return
    
    # Initialize YOLO detector (CPU)
    print("\n🔍 Initializing YOLO Detector (CPU)...")
    try:
        detector = ObjectDetector(
            config_file_path="F:/YOLO2K/src/config.json",
            model_path="F:/YOLO2K/models/yolov3.weights",
            config_path="F:/YOLO2K/models/yolov3.cfg",
            labels_path="F:/YOLO2K/models/coco.names"
        )
        print("✅ YOLO Detector initialized on CPU")
    except Exception as e:
        print(f"⚠️  YOLO initialization failed: {e}")
        print("💡 Using simulated detection data")
        detector = None
    
    # Initialize TensorFlow Decision AI (AMD GPU)
    print("\n🧠 Initializing Decision AI (AMD GPU via DirectML)...")
    model = DecisionAI_TF.create_optimized_model(
        num_actions=len(Action),
        input_size=100
    )
    
    # Monitor GPU usage
    print("\n📊 GPU Status:")
    monitor_gpu_usage()
    
    # Generate training data
    print("\n📚 Preparing Training Data...")
    states, actions, rewards = simulate_training_data(detector, num_samples=200)
    
    print(f"✅ Training data prepared:")
    print(f"  - States shape: {states.shape}")
    print(f"  - Actions shape: {actions.shape}")
    print(f"  - Rewards shape: {rewards.shape}")
    
    # Training loop
    print("\n🚀 Starting Training...")
    batch_size = 32
    epochs = 10
    
    # Use numpy arrays directly (no TensorFlow tensors needed)
    states_np = states.astype(np.float32)
    actions_np = actions.astype(np.int32)
    rewards_np = rewards.astype(np.float32)
    
    for epoch in range(epochs):
        print(f"\n📈 Epoch {epoch + 1}/{epochs}")
        
        # Shuffle data using numpy
        indices = np.random.permutation(len(states_np))
        states_shuffled = states_np[indices]
        actions_shuffled = actions_np[indices]
        rewards_shuffled = rewards_np[indices]
        
        epoch_loss = 0
        num_batches = len(states_np) // batch_size
        
        for batch in range(num_batches):
            start_idx = batch * batch_size
            end_idx = start_idx + batch_size
            
            batch_states = states_shuffled[start_idx:end_idx]
            batch_actions = actions_shuffled[start_idx:end_idx]
            batch_rewards = rewards_shuffled[start_idx:end_idx]
            
            # Training step
            loss = model.train_step(batch_states, batch_actions, batch_rewards)
            epoch_loss += loss
            
            if batch % 5 == 0:
                print(f"  Batch {batch + 1}/{num_batches}, Loss: {loss:.4f}")
        
        avg_loss = epoch_loss / num_batches if num_batches > 0 else 0
        print(f"  Average Loss: {avg_loss:.4f}")
        
        # Monitor GPU every few epochs
        if (epoch + 1) % 3 == 0:
            print("  📊 GPU Status:")
            monitor_gpu_usage()
    
    # Save the model
    print("\n💾 Saving Model...")
    model_path = "F:/YOLO2K/models/decision_ai_tf_amd.h5"
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    model.save_model(model_path)
    
    # Test the trained model
    print("\n🧪 Testing Trained Model...")
    test_objects = [
        {'class_id': 1, 'box': [400, 300, 50, 75]},
        {'class_id': 2, 'box': [200, 150, 80, 60]}
    ]
    
    test_state = DecisionAI_TF.preprocess_input(test_objects)
    predicted_action = model.select_action(test_state)
    action_name = Action(predicted_action).name
    
    print(f"✅ Test prediction: {action_name}")
    
    print("\n🎉 Training Complete!")
    print("=" * 60)
    print("📊 Summary:")
    print(f"  - Neural Network: AMD RX 580 GPU (DirectML)")
    print(f"  - YOLO Detection: CPU")
    print(f"  - Model saved to: {model_path}")
    print(f"  - Final loss: {avg_loss:.4f}")
    
    # Final GPU status
    print("\n📊 Final GPU Status:")
    monitor_gpu_usage()

if __name__ == "__main__":
    try:
        train_model()
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Training error: {e}")
        import traceback
        traceback.print_exc()