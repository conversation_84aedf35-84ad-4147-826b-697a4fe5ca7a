
import torch
import torch.nn as nn
import torch.nn.functional as F
from enum import Enum
import numpy as np
from gpu_optimizer import setup_amd_gpu, optimize_model_for_amd

# Define the list of discrete actions the AI can choose from
class Action(Enum):
    DO_NOTHING = 0
    MOVE_FORWARD = 1
    MOVE_BACKWARD = 2
    MOVE_LEFT = 3
    MOVE_RIGHT = 4
    PASS = 5  # Represents the 'A' button on an Xbox controller
    SHOOT = 6 # Represents the 'X' button on an Xbox controller
    SPRINT = 7 # Represents the right trigger

class DecisionAI(nn.Module):
    """
    A larger and more capable neural network for decision-making.
    This network takes flattened object detection data as input and outputs a probability
    distribution over the discrete actions defined in the Action Enum.
    """
    def __init__(self, num_actions, input_size=100):
        super(DecisionAI, self).__init__()
        
        # We assume a fixed-size input vector. 
        # e.g., max 20 objects, each with 5 features (class_id, x, y, w, h) -> 20*5=100
        self.input_size = input_size

        # A deeper and wider network architecture
        self.layer1 = nn.Linear(input_size, 512)
        self.layer2 = nn.Linear(512, 1024)
        self.layer3 = nn.Linear(1024, 512)
        self.output_layer = nn.Linear(512, num_actions)

    def forward(self, x):
        # Flatten the input if it's not already
        x = x.view(-1, self.input_size)
        
        # Forward pass through the network with ReLU activations
        x = F.relu(self.layer1(x))
        x = F.relu(self.layer2(x))
        x = F.relu(self.layer3(x))
        
        # The output is raw scores (logits) for each action
        action_scores = self.output_layer(x)
        return action_scores

    def select_action(self, state):
        """
        Selects an action based on the current state using an epsilon-greedy policy.
        This is used during training to balance exploration and exploitation.
        """
        # The model outputs logits. We use softmax to get probabilities.
        with torch.no_grad():
            action_probs = F.softmax(self.forward(state), dim=-1)
            # Choose the action with the highest probability
            action = torch.argmax(action_probs).item()
            return action

    @staticmethod
    def preprocess_input(detected_objects, max_objects=20, num_features=5):
        """
        Converts the list of detected objects into a fixed-size tensor for the network.
        """
        # Create a zero tensor of the required size
        input_tensor = torch.zeros(max_objects, num_features)

        # Populate the tensor with data from detected objects
        for i, obj in enumerate(detected_objects):
            if i >= max_objects:
                break
            input_tensor[i, 0] = obj['class_id']
            input_tensor[i, 1] = obj['box'][0] # x
            input_tensor[i, 2] = obj['box'][1] # y
            input_tensor[i, 3] = obj['box'][2] # width
            input_tensor[i, 4] = obj['box'][3] # height

        # Flatten the tensor to create a single vector
        return input_tensor.flatten()
    
    @classmethod
    def create_optimized_model(cls, num_actions, input_size=100):
        """
        Create a DecisionAI model optimized for AMD RX 580.
        """
        # Set up the optimal device
        device = setup_amd_gpu()
        
        # Create the model
        model = cls(num_actions=num_actions, input_size=input_size)
        
        # Apply AMD-specific optimizations
        model = optimize_model_for_amd(model, device)
        
        return model, device
