
import vgamepad as vg
import inputs
import threading
import time
import math
from decision_ai import Action # Import the Action enum

class ActionMapper:
    """
    Translates high-level AI actions into low-level controller inputs.
    """
    @staticmethod
    def execute_action(action: Action, gamepad: vg.VX360Gamepad):
        """
        Resets the gamepad and executes the specified action.
        """
        gamepad.reset() # Reset all controls before executing a new action

        if action == Action.MOVE_FORWARD:
            gamepad.left_joystick_float(x_value_float=0.0, y_value_float=1.0)
        elif action == Action.MOVE_BACKWARD:
            gamepad.left_joystick_float(x_value_float=0.0, y_value_float=-1.0)
        elif action == Action.MOVE_LEFT:
            gamepad.left_joystick_float(x_value_float=-1.0, y_value_float=0.0)
        elif action == Action.MOVE_RIGHT:
            gamepad.left_joystick_float(x_value_float=1.0, y_value_float=0.0)
        elif action == Action.PASS:
            gamepad.press_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
        elif action == Action.SHOOT:
            gamepad.press_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_X)
        elif action == Action.SPRINT:
            gamepad.right_trigger_float(value_float=1.0)
        # DO_NOTHING requires no action as the gamepad is reset

        gamepad.update()

class InputSimulator:
    """
    Manages a virtual Xbox 360 controller and passes through
    physical controller inputs, allowing the bot to override them.
    """
    def __init__(self):
        print("Initializing Input Simulator...")
        self.gamepad = vg.VX360Gamepad()
        self.physical_controller = self._find_physical_controller()
        self.bot_active = False
        self.running = True

        # Start a separate thread to handle physical controller passthrough
        self.passthrough_thread = threading.Thread(target=self._passthrough_worker, daemon=True)
        self.passthrough_thread.start()
        print("Input Simulator initialized. Controller passthrough is active.")

    def _find_physical_controller(self):
        try:
            if not inputs.devices.gamepads:
                print("Warning: No physical controller found! Passthrough will be disabled.")
                return None
            print(f"Found physical controller: {inputs.devices.gamepads[0].name}")
            return inputs.devices.gamepads[0]
        except Exception as e:
            print(f"Error finding controller: {e}")
            return None

    def _passthrough_worker(self):
        if not self.physical_controller:
            return

        while self.running:
            if self.bot_active:
                time.sleep(0.01)
                continue
            try:
                events = self.physical_controller.read()
                for event in events:
                    self._map_event_to_virtual_controller(event)
                self.gamepad.update()
            except (EOFError, OSError):
                print("Physical controller disconnected. Attempting to reconnect...")
                self.physical_controller = self._find_physical_controller()
                time.sleep(5)
            except Exception as e:
                print(f"Error in passthrough worker: {e}")

    def _map_event_to_virtual_controller(self, event):
        # This mapping is for a standard XInput controller
        # This function would need to be filled out similar to the original if passthrough is needed
        pass # Simplified for brevity

    def execute_bot_command(self, action: Action):
        """
        Takes over the controller to execute an action from the bot using the ActionMapper.
        """
        self.bot_active = True
        ActionMapper.execute_action(action, self.gamepad)

    def release_bot_control(self):
        """
        Releases bot control, resets the gamepad, and allows physical passthrough to resume.
        """
        self.bot_active = False
        self.gamepad.reset()
        self.gamepad.update()

    def shutdown(self):
        print("Shutting down Input Simulator...")
        self.running = False
        if self.passthrough_thread.is_alive():
            self.passthrough_thread.join(timeout=1)
        self.gamepad.reset()
        self.gamepad.update()
        print("Input Simulator shut down.")
