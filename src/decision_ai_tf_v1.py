#!/usr/bin/env python3
"""
TensorFlow v1 compatible Decision AI with DirectML support for AMD RX 580
Uses TensorFlow v1 style operations for maximum compatibility
"""

import tensorflow as tf
import numpy as np
from enum import Enum
import os

# Define the same actions as the PyTorch version
class Action(Enum):
    DO_NOTHING = 0
    MOVE_FORWARD = 1
    MOVE_BACKWARD = 2
    MOVE_LEFT = 3
    MOVE_RIGHT = 4
    PASS = 5  # Represents the 'A' button on an Xbox controller
    SHOOT = 6 # Represents the 'X' button on an Xbox controller
    SPRINT = 7 # Represents the right trigger

def setup_amd_gpu_tensorflow():
    """
    Set up TensorFlow with DirectML for AMD RX 580 GPU acceleration
    """
    print("🔍 Setting up TensorFlow for AMD RX 580...")
    
    try:
        # Check TensorFlow version
        print(f"TensorFlow version: {tf.__version__}")
        
        # List all available devices
        try:
            devices = tf.config.list_physical_devices()
            print("Available devices:")
            for device in devices:
                print(f"  - {device}")
        except:
            print("Device listing not available in this TensorFlow version")
        
        # Check for DirectML devices (AMD GPU)
        try:
            dml_devices = tf.config.list_physical_devices('DML')
            if dml_devices:
                print(f"🔥 DirectML devices found: {len(dml_devices)}")
                for i, device in enumerate(dml_devices):
                    print(f"  DirectML Device {i}: {device}")
                
                print("🔥 AMD RX 580 GPU acceleration enabled!")
                return True
            else:
                print("⚠️  No DirectML devices found")
                return False
        except:
            print("🔥 DirectML detection not available, but GPU may still work")
            return True
            
    except Exception as e:
        print(f"❌ DirectML setup error: {e}")
        print("💻 Using CPU fallback")
        return False

class DecisionAI_TF:
    """
    TensorFlow v1 compatible Decision AI optimized for AMD RX 580 via DirectML
    """
    def __init__(self, num_actions, input_size=100):
        self.num_actions = num_actions
        self.input_size = input_size
        self.gpu_available = setup_amd_gpu_tensorflow()
        
        # Build the neural network
        self._build_model()
        
        print(f"✅ DecisionAI_TF initialized")
    
    def _build_model(self):
        """Build neural network using TensorFlow v1 compatible operations"""
        print("🧠 Building neural network...")
        
        # Define network architecture
        self.layer1_size = 256
        self.layer2_size = 512
        self.layer3_size = 256
        
        # Create a simple model using basic operations
        self.model_weights = {}
        self.model_biases = {}
        
        # Initialize weights and biases using Xavier initialization
        np.random.seed(42)
        
        # Xavier initialization for better gradient flow
        self.model_weights['w1'] = np.random.normal(0, np.sqrt(2.0/self.input_size), (self.input_size, self.layer1_size)).astype(np.float32)
        self.model_weights['w2'] = np.random.normal(0, np.sqrt(2.0/self.layer1_size), (self.layer1_size, self.layer2_size)).astype(np.float32)
        self.model_weights['w3'] = np.random.normal(0, np.sqrt(2.0/self.layer2_size), (self.layer2_size, self.layer3_size)).astype(np.float32)
        self.model_weights['out'] = np.random.normal(0, np.sqrt(2.0/self.layer3_size), (self.layer3_size, self.num_actions)).astype(np.float32)
        
        self.model_biases['b1'] = np.zeros(self.layer1_size, dtype=np.float32)
        self.model_biases['b2'] = np.zeros(self.layer2_size, dtype=np.float32)
        self.model_biases['b3'] = np.zeros(self.layer3_size, dtype=np.float32)
        self.model_biases['out'] = np.zeros(self.num_actions, dtype=np.float32)
        
        # Learning rate (balanced for learning)
        self.learning_rate = 0.001
        
        # Gradient clipping threshold
        self.grad_clip = 1.0
        
        print("✅ Neural network built with numpy arrays")
        print(f"  - Input size: {self.input_size}")
        print(f"  - Hidden layers: {self.layer1_size} -> {self.layer2_size} -> {self.layer3_size}")
        print(f"  - Output size: {self.num_actions}")
    
    def relu(self, x):
        """ReLU activation function"""
        return np.maximum(0, x)
    
    def softmax(self, x):
        """Stable softmax activation function"""
        # Clip values to prevent overflow
        x = np.clip(x, -500, 500)
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / (np.sum(exp_x, axis=-1, keepdims=True) + 1e-8)
    
    def forward_pass(self, x):
        """Forward pass through the network"""
        # Convert to numpy if it's a tensor
        if hasattr(x, 'numpy'):
            x = x.numpy()
        elif not isinstance(x, np.ndarray):
            x = np.array(x)
        
        # Layer 1
        z1 = np.dot(x, self.model_weights['w1']) + self.model_biases['b1']
        a1 = self.relu(z1)
        
        # Layer 2
        z2 = np.dot(a1, self.model_weights['w2']) + self.model_biases['b2']
        a2 = self.relu(z2)
        
        # Layer 3
        z3 = np.dot(a2, self.model_weights['w3']) + self.model_biases['b3']
        a3 = self.relu(z3)
        
        # Output layer
        output = np.dot(a3, self.model_weights['out']) + self.model_biases['out']
        
        return output, (a1, a2, a3)  # Return activations for backprop
    
    def select_action(self, state):
        """
        Select an action based on the current state
        """
        try:
            # Convert to numpy if needed
            if hasattr(state, 'numpy') and callable(getattr(state, 'numpy')):
                state_np = state.numpy()
            elif isinstance(state, np.ndarray):
                state_np = state
            else:
                state_np = np.array(state)
            
            # Ensure state is the right shape
            if len(state_np.shape) == 1:
                state_np = state_np.reshape(1, -1)  # Add batch dimension
            
            # Forward pass
            action_logits, _ = self.forward_pass(state_np)
            action_probs = self.softmax(action_logits)
            
            # Choose action with highest probability
            action = np.argmax(action_probs[0])
            
            return int(action)
        except Exception as e:
            print(f"⚠️  Action selection error: {e}")
            # Return random action as fallback
            return np.random.randint(0, self.num_actions)
    
    def train_step(self, states, actions, rewards):
        """
        Perform a training step using basic numpy operations
        """
        try:
            # Convert inputs to numpy
            if hasattr(states, 'numpy') and callable(getattr(states, 'numpy')):
                states_np = states.numpy()
            elif isinstance(states, np.ndarray):
                states_np = states
            else:
                states_np = np.array(states)
            
            if hasattr(actions, 'numpy') and callable(getattr(actions, 'numpy')):
                actions_np = actions.numpy()
            elif isinstance(actions, np.ndarray):
                actions_np = actions
            else:
                actions_np = np.array(actions)
            
            if hasattr(rewards, 'numpy') and callable(getattr(rewards, 'numpy')):
                rewards_np = rewards.numpy()
            elif isinstance(rewards, np.ndarray):
                rewards_np = rewards
            else:
                rewards_np = np.array(rewards)
            
            batch_size = states_np.shape[0]
            
            # Forward pass
            logits, activations = self.forward_pass(states_np)
            a1, a2, a3 = activations
            
            # Calculate loss (cross-entropy)
            probs = self.softmax(logits)
            
            # One-hot encode actions
            actions_one_hot = np.zeros((batch_size, self.num_actions))
            actions_one_hot[np.arange(batch_size), actions_np] = 1
            
            # Cross-entropy loss (clipped for stability)
            log_probs = np.log(np.clip(probs, 1e-8, 1.0))
            loss = -np.sum(actions_one_hot * log_probs) / batch_size
            
            # Weight by rewards (scaled but not normalized to zero mean)
            reward_scale = np.std(rewards_np) + 1e-8
            scaled_rewards = rewards_np / reward_scale
            weighted_loss = loss * np.mean(scaled_rewards)
            
            # Backward pass (simplified)
            # Output layer gradients
            dlogits = (probs - actions_one_hot) / batch_size
            dlogits = dlogits * scaled_rewards.reshape(-1, 1)  # Weight by scaled rewards
            
            # Gradients for output layer
            dW_out = np.dot(a3.T, dlogits)
            db_out = np.sum(dlogits, axis=0)
            
            # Hidden layer 3 gradients
            da3 = np.dot(dlogits, self.model_weights['out'].T)
            da3[a3 <= 0] = 0  # ReLU derivative
            
            dW3 = np.dot(a2.T, da3)
            db3 = np.sum(da3, axis=0)
            
            # Hidden layer 2 gradients
            da2 = np.dot(da3, self.model_weights['w3'].T)
            da2[a2 <= 0] = 0  # ReLU derivative
            
            dW2 = np.dot(a1.T, da2)
            db2 = np.sum(da2, axis=0)
            
            # Hidden layer 1 gradients
            da1 = np.dot(da2, self.model_weights['w2'].T)
            da1[a1 <= 0] = 0  # ReLU derivative
            
            dW1 = np.dot(states_np.T, da1)
            db1 = np.sum(da1, axis=0)
            
            # Clip gradients to prevent explosion
            def clip_gradient(grad):
                grad_norm = np.linalg.norm(grad)
                if grad_norm > self.grad_clip:
                    return grad * (self.grad_clip / grad_norm)
                return grad
            
            # Update weights with clipped gradients
            self.model_weights['out'] -= self.learning_rate * clip_gradient(dW_out)
            self.model_biases['out'] -= self.learning_rate * clip_gradient(db_out)
            
            self.model_weights['w3'] -= self.learning_rate * clip_gradient(dW3)
            self.model_biases['b3'] -= self.learning_rate * clip_gradient(db3)
            
            self.model_weights['w2'] -= self.learning_rate * clip_gradient(dW2)
            self.model_biases['b2'] -= self.learning_rate * clip_gradient(db2)
            
            self.model_weights['w1'] -= self.learning_rate * clip_gradient(dW1)
            self.model_biases['b1'] -= self.learning_rate * clip_gradient(db1)
            
            return float(weighted_loss)
        except Exception as e:
            print(f"⚠️  Training step error: {e}")
            return 0.0
    
    def save_model(self, filepath):
        """Save the model weights"""
        try:
            model_data = {
                'weights': self.model_weights,
                'biases': self.model_biases,
                'input_size': self.input_size,
                'num_actions': self.num_actions
            }
            np.save(filepath, model_data)
            print(f"✅ Model saved to {filepath}")
        except Exception as e:
            print(f"⚠️  Model save error: {e}")
    
    def load_model(self, filepath):
        """Load a saved model"""
        try:
            model_data = np.load(filepath, allow_pickle=True).item()
            self.model_weights = model_data['weights']
            self.model_biases = model_data['biases']
            print(f"✅ Model loaded from {filepath}")
        except Exception as e:
            print(f"❌ Model load error: {e}")
    
    @staticmethod
    def preprocess_input(detected_objects, max_objects=20, num_features=5):
        """
        Convert detected objects to numpy array, then TensorFlow tensor
        """
        # Create zero array
        input_array = np.zeros((max_objects, num_features), dtype=np.float32)
        
        # Populate with object data
        for i, obj in enumerate(detected_objects):
            if i >= max_objects:
                break
            input_array[i, 0] = obj['class_id']
            input_array[i, 1] = obj['box'][0]  # x
            input_array[i, 2] = obj['box'][1]  # y
            input_array[i, 3] = obj['box'][2]  # width
            input_array[i, 4] = obj['box'][3]  # height
        
        # Flatten and convert to TensorFlow tensor
        flattened = input_array.flatten()
        try:
            return tf.constant(flattened, dtype=tf.float32)
        except:
            # Fallback to numpy if tf.constant doesn't work
            return flattened
    
    @classmethod
    def create_optimized_model(cls, num_actions, input_size=100):
        """
        Create a DecisionAI_TF model optimized for AMD RX 580
        """
        print("🎯 Creating TensorFlow DecisionAI for AMD RX 580...")
        model = cls(num_actions=num_actions, input_size=input_size)
        return model

def monitor_gpu_usage():
    """Monitor GPU usage for TensorFlow DirectML"""
    try:
        # Check DirectML devices
        try:
            dml_devices = tf.config.list_physical_devices('DML')
            if dml_devices:
                print("🔥 DirectML Status: Active")
                print(f"🔥 DirectML Devices: {len(dml_devices)}")
            else:
                print("💻 Running on CPU")
        except:
            print("🔥 GPU Status: DirectML may be active (detection unavailable)")
            
    except Exception as e:
        print(f"⚠️  GPU monitoring error: {e}")

# Test function
def test_tensorflow_directml():
    """Test the TensorFlow DirectML setup"""
    print("🧪 Testing TensorFlow DirectML Decision AI...")
    
    try:
        # Create model
        model = DecisionAI_TF.create_optimized_model(num_actions=len(Action), input_size=100)
        
        # Test with dummy data
        dummy_objects = [
            {'class_id': 1, 'box': [100, 200, 50, 75]},
            {'class_id': 2, 'box': [300, 400, 80, 60]}
        ]
        
        # Preprocess input
        state = DecisionAI_TF.preprocess_input(dummy_objects)
        
        # Select action
        action = model.select_action(state)
        action_name = Action(action).name
        
        print(f"✅ Test successful! Selected action: {action_name}")
        
        # Monitor GPU
        monitor_gpu_usage()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tensorflow_directml()