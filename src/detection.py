import cv2
import numpy as np
import json
import dxcam

class ObjectDetector:
    """
    This class handles object detection using the YOLO model,
    capturing a specific region of the screen defined in a config file.
    """
    def __init__(self, config_file_path, model_path, config_path, labels_path):
        # Load configuration
        with open(config_file_path, 'r') as f:
            config = json.load(f)
        
        main_region = config.get("main_capture_region")
        if not main_region:
            raise ValueError("Configuration file must contain 'main_capture_region'")
            
        # DXCam for screen capture of the defined region
        left, top, right, bottom = main_region
        self.region = (left, top, right, bottom)
        self.camera = dxcam.create(region=self.region)

        # Force CPU usage for YOLO detection (Neural Network will use GPU)
        try:
            cv2.dnn.setPreferableBackend(cv2.dnn.DNN_BACKEND_OPENCV)
            cv2.dnn.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
            print("✅ OpenCV DNN configured for CPU (Neural Network will use AMD GPU)")
        except AttributeError:
            print("⚠️  OpenCV version doesn't support setPreferableBackend - using default backend")
        except Exception as e:
            print(f"⚠️  Could not set CPU backend: {e} - using default backend")
        
        self.net = cv2.dnn.readNetFromDarknet(config_path, model_path)
        self.output_layers = self._get_output_layers()
        with open(labels_path, 'r') as f:
            self.classes = [line.strip() for line in f.readlines()]
    


    def _get_output_layers(self):
        layer_names = self.net.getLayerNames()
        try:
            # For OpenCV 4.x and later, getUnconnectedOutLayers() returns a tuple of arrays.
            output_layer_indexes = self.net.getUnconnectedOutLayers()
            if isinstance(output_layer_indexes, tuple):
                 return [layer_names[i[0] - 1] for i in output_layer_indexes]
            else: # For older versions, it returns a numpy array
                 return [layer_names[i - 1] for i in output_layer_indexes]
        except Exception:
            # Fallback for maximum compatibility
            return [layer_names[i[0] - 1] for i in self.net.getUnconnectedOutLayers()]

    def capture_and_detect(self, conf_threshold=0.5, nms_threshold=0.4):
        # Capture a frame from the specified region
        frame = self.camera.grab(self.region)

        if frame is None:
            return [], None # Return empty list and no frame

        height, width, _ = frame.shape
        
        # Create blob with OpenCL optimization
        blob = cv2.dnn.blobFromImage(frame, 1 / 255.0, (416, 416), swapRB=True, crop=False)
        
        # Use OpenCL-optimized inference if available
        self.net.setInput(blob)
        
        # Forward pass - this will use OpenCL if configured
        layer_outputs = self.net.forward(self.output_layers)

        boxes = []
        confidences = []
        class_ids = []

        for output in layer_outputs:
            for detection in output:
                scores = detection[5:]
                class_id = np.argmax(scores)
                confidence = scores[class_id]
                if confidence > conf_threshold:
                    center_x = int(detection[0] * width)
                    center_y = int(detection[1] * height)
                    w = int(detection[2] * width)
                    h = int(detection[3] * height)
                    x = int(center_x - w / 2)
                    y = int(center_y - h / 2)
                    boxes.append([x, y, w, h])
                    confidences.append(float(confidence))
                    class_ids.append(class_id)

        indices = cv2.dnn.NMSBoxes(boxes, confidences, conf_threshold, nms_threshold)

        detected_objects = []
        if len(indices) > 0:
            indices = indices.flatten()
            for i in indices:
                box = boxes[i]
                x, y, w, h = box[0], box[1], box[2], box[3]
                detected_objects.append({
                    "label": str(self.classes[class_ids[i]]),
                    "class_id": class_ids[i],
                    "confidence": confidences[i],
                    "box": [x, y, w, h]
                })
        
        return detected_objects, frame
