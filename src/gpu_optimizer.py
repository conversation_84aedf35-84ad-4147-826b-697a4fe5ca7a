"""
GPU Optimization utilities for AMD RX 580 and PyTorch.
This module helps configure PyTorch to work optimally with AMD GPUs.
"""

import torch
import os
import warnings

def setup_amd_gpu():
    """
    Configure PyTorch for optimal performance on AMD RX 580.
    Returns the best available device and prints configuration info.
    """
    print("=== GPU Configuration ===")
    
    # Check CUDA availability (works with AMD via ROCm)
    if torch.cuda.is_available():
        device = torch.device("cuda")
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        print(f"✓ GPU Available: {gpu_name}")
        print(f"✓ GPU Memory: {gpu_memory:.1f} GB")
        print(f"✓ CUDA Version: {torch.version.cuda}")
        
        # Optimize for AMD GPU
        torch.backends.cudnn.benchmark = True  # Optimize for consistent input sizes
        torch.backends.cudnn.deterministic = False  # Allow non-deterministic for speed
        
        # Set memory management for AMD GPUs
        if hasattr(torch.cuda, 'empty_cache'):
            torch.cuda.empty_cache()
            
        # AMD-specific optimizations
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        
        return device
    else:
        print("⚠ No GPU available, using CPU")
        print("For AMD RX 580 support, install ROCm or use CPU with optimizations")
        
        # CPU optimizations
        torch.set_num_threads(4)  # Adjust based on your CPU
        return torch.device("cpu")

def optimize_model_for_amd(model, device):
    """
    Apply AMD-specific optimizations to a PyTorch model.
    """
    model = model.to(device)
    
    if device.type == 'cuda':
        # Enable mixed precision for AMD GPUs if supported
        try:
            model = torch.jit.script(model)  # JIT compilation for speed
            print("✓ JIT compilation enabled")
        except Exception as e:
            print(f"⚠ JIT compilation failed: {e}")
    
    # Set model to evaluation mode for inference
    model.eval()
    
    return model

def get_optimal_batch_size(device, model_size="medium"):
    """
    Get optimal batch size based on GPU memory and model size.
    """
    if device.type == 'cuda':
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        # RX 580 has 8GB VRAM, adjust batch size accordingly
        if gpu_memory_gb >= 7:  # RX 580 8GB
            batch_sizes = {"small": 32, "medium": 16, "large": 8}
        elif gpu_memory_gb >= 3:  # RX 580 4GB
            batch_sizes = {"small": 16, "medium": 8, "large": 4}
        else:
            batch_sizes = {"small": 8, "medium": 4, "large": 2}
            
        return batch_sizes.get(model_size, 8)
    else:
        return 4  # Conservative batch size for CPU

def monitor_gpu_usage():
    """
    Monitor GPU memory usage (useful for debugging).
    """
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        print(f"GPU Memory - Allocated: {allocated:.2f}GB, Cached: {cached:.2f}GB")
    else:
        print("GPU monitoring not available (CPU mode)")

class AMDOptimizedTraining:
    """
    Training utilities optimized for AMD RX 580.
    """
    def __init__(self, model, device):
        self.model = model
        self.device = device
        self.scaler = None
        
        # Enable automatic mixed precision if supported
        if device.type == 'cuda':
            try:
                self.scaler = torch.cuda.amp.GradScaler()
                print("✓ Mixed precision training enabled")
            except:
                print("⚠ Mixed precision not available")
    
    def forward_pass(self, inputs):
        """Optimized forward pass with mixed precision if available."""
        if self.scaler is not None:
            with torch.cuda.amp.autocast():
                return self.model(inputs)
        else:
            return self.model(inputs)
    
    def backward_pass(self, loss, optimizer):
        """Optimized backward pass with gradient scaling."""
        if self.scaler is not None:
            self.scaler.scale(loss).backward()
            self.scaler.step(optimizer)
            self.scaler.update()
        else:
            loss.backward()
            optimizer.step()

# Environment setup for AMD GPUs
def setup_environment():
    """Set up environment variables for optimal AMD GPU performance."""
    
    # ROCm optimizations
    os.environ['HSA_OVERRIDE_GFX_VERSION'] = '8.0.3'  # For RX 580 compatibility
    os.environ['PYTORCH_HIP_ALLOC_CONF'] = 'max_split_size_mb:128'
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings("ignore", category=UserWarning)
    
    print("✓ Environment configured for AMD RX 580")

if __name__ == "__main__":
    # Test the GPU setup
    setup_environment()
    device = setup_amd_gpu()
    print(f"Selected device: {device}")
    monitor_gpu_usage()