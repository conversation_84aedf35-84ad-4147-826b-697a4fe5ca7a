
import cv2
import json
import dxcam
import numpy as np

# --- Global Variables ---
ref_point = []
cropping = False
image = None
camera = None

def click_and_crop(event, x, y, flags, param):
    """Callback function for mouse events."""
    global ref_point, cropping, image

    if event == cv2.EVENT_LBUTTONDOWN:
        ref_point = [(x, y)]
        cropping = True

    elif event == cv2.EVENT_LBUTTONUP:
        ref_point.append((x, y))
        cropping = False

        # Draw a rectangle around the region of interest
        cv2.rectangle(image, ref_point[0], ref_point[1], (0, 255, 0), 2)
        cv2.imshow("image", image)

def capture_screen():
    """Capture the full screen using DXCam."""
    global camera
    
    if camera is None:
        camera = dxcam.create()
    
    frame = camera.grab()
    if frame is not None:
        # Convert from RGB to BGR for OpenCV
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
    
    return frame

def main():
    """Main function to run the region selector."""
    global image, camera

    regions = {
        "main_capture_region": None,
        "ocr_region_1": None,
        "ocr_region_2": None
    }

    region_names = {
        "main_capture_region": "Main YOLOv4 Capture Region",
        "ocr_region_1": "First OCR Region (e.g., Scoreboard)",
        "ocr_region_2": "Second OCR Region (e.g., Feedback Text)"
    }

    print("Initializing DXCam for screen capture...")
    try:
        image = capture_screen()
        if image is None:
            print("ERROR: Failed to capture screen with DXCam")
            print("Make sure you have a compatible graphics card and drivers")
            return
        print(f"✅ Screen captured successfully: {image.shape}")
    except Exception as e:
        print(f"ERROR: DXCam initialization failed: {e}")
        print("Make sure DXCam is properly installed: pip install dxcam")
        return

    clone = image.copy()
    cv2.namedWindow("image")
    cv2.setMouseCallback("image", click_and_crop)

    print("Region Selector Started with DXCam.")
    print("Controls:")
    print("  - Click and drag to select a region")
    print("  - Press 'c' to confirm the current selection")
    print("  - Press 'r' to reset/redraw the current region")
    print("  - Press 'f' to refresh the screen capture")
    print("  - Press 'q' to quit (will lose progress)")

    for region_key, region_name in region_names.items():
        print(f"\n📐 Please draw the box for: {region_name}")
        cv2.setWindowTitle("image", f"Draw Box for: {region_name} (c=confirm, r=reset, f=refresh)")

        while True:
            cv2.imshow("image", image)
            key = cv2.waitKey(1) & 0xFF

            if key == ord("r"):  # Reset the current crop
                image = clone.copy()
                ref_point = []  # Clear the reference points
                print("Region reset. Please draw it again.")

            elif key == ord("f"):  # Refresh screen capture
                print("Refreshing screen capture...")
                try:
                    new_image = capture_screen()
                    if new_image is not None:
                        image = new_image
                        clone = image.copy()
                        ref_point = []  # Clear any existing selection
                        print("✅ Screen refreshed successfully")
                    else:
                        print("❌ Failed to refresh screen")
                except Exception as e:
                    print(f"❌ Error refreshing screen: {e}")

            elif key == ord("c"):  # Confirm the crop
                if len(ref_point) == 2:
                    x1, y1 = ref_point[0]
                    x2, y2 = ref_point[1]
                    # Ensure x1 < x2 and y1 < y2
                    region = (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2))
                    regions[region_key] = region
                    print(f"✅ Region '{region_name}' saved: {region}")
                    
                    # Reset for next region
                    ref_point = []
                    image = clone.copy()  # Clear the drawn rectangle
                    break  # Move to the next region
                else:
                    print("❌ No region selected to confirm. Please draw a box first.")

            elif key == ord("q"):  # Quit
                print("❌ Quitting region selector...")
                cv2.destroyAllWindows()
                if camera:
                    camera.release()
                return

    cv2.destroyAllWindows()
    
    # Clean up DXCam resources
    if camera:
        camera.release()

    # Validate that all regions were set
    missing_regions = [key for key, value in regions.items() if value is None]
    if missing_regions:
        print(f"\n❌ Warning: The following regions were not configured: {missing_regions}")
        print("The configuration file will still be saved, but you may need to run this again.")

    # Save the coordinates to a config file
    config_path = 'F:/YOLO2K/src/config.json'
    try:
        with open(config_path, 'w') as f:
            json.dump(regions, f, indent=4)
        
        print(f"\n✅ Configuration saved successfully to {config_path}")
        print("\nRegion Summary:")
        for key, value in regions.items():
            if value:
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: ❌ Not configured")
        
        print(f"\n🎯 Next Steps:")
        print("1. Test OCR: python src/ocr_reader.py")
        print("2. Test detection: python src/main.py")
        print("3. Start training: python src/train.py")
        
    except Exception as e:
        print(f"\n❌ Error saving configuration: {e}")
        print("Please check file permissions and try again.")

def test_dxcam():
    """Test DXCam functionality."""
    print("Testing DXCam functionality...")
    try:
        camera = dxcam.create()
        frame = camera.grab()
        if frame is not None:
            print(f"✅ DXCam working - captured frame: {frame.shape}")
            camera.release()
            return True
        else:
            print("❌ DXCam failed to capture frame")
            return False
    except Exception as e:
        print(f"❌ DXCam test failed: {e}")
        return False

if __name__ == "__main__":
    print("🖥️  YOLO2K Region Selector (DXCam Version)")
    print("=" * 50)
    
    # Test DXCam first
    if not test_dxcam():
        print("\n❌ DXCam is not working properly.")
        print("Please ensure:")
        print("1. DXCam is installed: pip install dxcam")
        print("2. You have a compatible graphics card")
        print("3. Graphics drivers are up to date")
        print("4. You're running on Windows")
        exit(1)
    
    print("\n🚀 Starting region selector...")
    main()
