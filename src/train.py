import torch
import torch.optim as optim
import numpy as np
import time
import json

# Import our custom modules
from detection import ObjectDetector
from decision_ai import DecisionAI, Action
from input_simulation import InputSimulator
from ocr_reader import OCRReader
from gpu_optimizer import setup_environment, AMDOptimizedTraining, monitor_gpu_usage

class OCRRewardSystem:
    """
    Uses text feedback from the game to determine the reward.
    This is the MOST IMPORTANT file for you to customize.
    """
    def __init__(self):
        self.reward_keywords = {
            # --- Positive Rewards (Good Plays) ---
            "good shot selection": 5.0, "green light": 10.0, "swish": 10.0,
            "made shot": 8.0, "assist": 7.0, "steal": 8.0, "block": 8.0,
            "good pass": 4.0,

            # --- Negative Rewards (Bad Plays) ---
            "turnover": -10.0, "bad pass": -8.0, "shot clock violation": -10.0,
            "heavily contested": -5.0, "bad shot selection": -7.0,
            "blocked shot": -6.0, "lost ball": -9.0
        }
        self.time_penalty = -0.01

    def get_reward(self, ocr_reader):
        """
        Calculates a reward by reading from the primary OCR region.
        Args:
            ocr_reader (OCRReader): The initialized OCR reader instance.
        Returns:
            float: The calculated reward value.
        """
        reward = self.time_penalty
        # Read from the primary feedback window (region 1)
        ocr_text = ocr_reader.read_text_from_region(1)

        if not ocr_text:
            return reward

        for keyword, value in self.reward_keywords.items():
            if keyword in ocr_text:
                print(f"REWARD EVENT: Detected '{keyword}'. Reward: {value}")
                return value
        return reward

def main():
    # --- Configuration ---
    CONFIG_PATH = "F:/YOLO2K/src/config.json"
    MODEL_PATH = "F:/YOLO2K/models/yolov4.weights"
    MODEL_CONFIG_PATH = "F:/YOLO2K/models/yolov4.cfg"
    LABELS_PATH = "F:/YOLO2K/models/coco.names"
    TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe' #<-- CHECK THIS PATH

    # --- Setup Environment for AMD GPU ---
    setup_environment()
    
    # --- Initialization ---
    print("Initializing components from config...")
    try:
        detector = ObjectDetector(CONFIG_PATH, MODEL_PATH, MODEL_CONFIG_PATH, LABELS_PATH)
        ocr_reader = OCRReader(CONFIG_PATH, TESSERACT_PATH)
        print("Object Detector and OCR Reader initialized successfully.")
    except FileNotFoundError:
        print(f"FATAL: config.json not found at '{CONFIG_PATH}'")
        print("Please run region_selector.py first to generate the config file.")
        return
    except Exception as e:
        print(f"FATAL: Could not initialize components: {e}")
        return

    # Create optimized model for AMD RX 580
    num_actions = len(Action)
    decision_ai, device = DecisionAI.create_optimized_model(num_actions=num_actions)
    
    # Set up AMD-optimized training
    amd_trainer = AMDOptimizedTraining(decision_ai, device)
    
    input_simulator = InputSimulator()
    reward_system = OCRRewardSystem()

    # --- Training Parameters ---
    optimizer = optim.Adam(decision_ai.parameters(), lr=5e-5)
    num_episodes = 5000
    gamma = 0.99

    print("--- Starting Training ---")
    print(f"Device: {device}")
    print("Press Ctrl+C to stop.")

    # --- Training Loop ---
    for episode in range(num_episodes):
        log_probs = []
        rewards = []
        episode_reward = 0

        try:
            for t in range(500): # Each episode is 500 steps
                # 1. AI sees the game and gets the state
                detected_objects, _ = detector.capture_and_detect()
                state_tensor = DecisionAI.preprocess_input(detected_objects).to(device)

                # 2. AI decides on an action (using AMD-optimized forward pass)
                action_logits = amd_trainer.forward_pass(state_tensor)
                action_probs = torch.softmax(action_logits, dim=-1)
                dist = torch.distributions.Categorical(action_probs)
                action_index = dist.sample()
                log_probs.append(dist.log_prob(action_index))
                
                # 3. Execute the action in the game
                action_enum = Action(action_index.item())
                input_simulator.execute_bot_command(action_enum)

                # 4. Get reward from OCR feedback
                reward = reward_system.get_reward(ocr_reader)
                rewards.append(reward)
                episode_reward += reward

                time.sleep(0.1) # Small delay between actions

            # --- Policy Update (The Learning Step) ---
            returns = []
            discounted_reward = 0
            for r in reversed(rewards):
                discounted_reward = r + gamma * discounted_reward
                returns.insert(0, discounted_reward)
            
            returns = torch.tensor(returns, device=device)
            returns = (returns - returns.mean()) / (returns.std() + 1e-9)

            policy_loss = [-log_prob * R for log_prob, R in zip(log_probs, returns)]
            
            optimizer.zero_grad()
            policy_loss = torch.stack(policy_loss).sum()
            
            # Use AMD-optimized backward pass
            amd_trainer.backward_pass(policy_loss, optimizer)

            print(f"Episode {episode + 1}: Total Reward = {episode_reward:.2f}, Loss = {policy_loss.item():.4f}")

            if (episode + 1) % 10 == 0:
                torch.save(decision_ai.state_dict(), 'decision_model.pth')
                print(f"Model saved to decision_model.pth")
                monitor_gpu_usage()  # Monitor GPU memory usage

        except KeyboardInterrupt:
            print("\nTraining interrupted. Saving final model...")
            torch.save(decision_ai.state_dict(), 'decision_model_final.pth')
            break
        except Exception as e:
            print(f"An error occurred during training: {e}")
            break
            
    input_simulator.shutdown()
    print("Training finished.")

if __name__ == "__main__":
    main()
