import cv2
import numpy as np
import pytesseract
import mss
import json
import re
import re

class OCRReader:
    """
    Reads text from specific screen regions defined in a config file.
    Uses MSS for reliable screen capture without COM interface issues.
    Includes reward system based on detected text patterns.
    """
    def __init__(self, config_file_path, tesseract_path):
        """
        Initializes the OCRReader.

        Args:
            config_file_path (str): Path to the JSON config file.
            tesseract_path (str): The full path to the Tesseract OCR executable.
        """
        # Load configuration for OCR regions
        with open(config_file_path, 'r') as f:
            config = json.load(f)
        
        self.region1 = tuple(config.get("ocr_region_1"))
        self.region2 = tuple(config.get("ocr_region_2"))

        if not self.region1 or not self.region2:
            raise ValueError("Config file must contain 'ocr_region_1' and 'ocr_region_2'")

        # Initialize MSS for screen capture
        try:
            self.sct = mss.mss()
        except Exception as e:
            print(f"Warning: MSS initialization failed: {e}")
            self.sct = None
        
        if not tesseract_path:
            raise ValueError("Tesseract executable path cannot be empty.")
        
        # Set the path to the Tesseract executable
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Initialize reward dictionary
        self.reward_terms = {
            # Positive rewards (good plays)
            'positive': {
                'scored': 1.0,
                'basket': 1.0,
                'made': 0.8,
                'good': 0.5,
                'nice': 0.5,
                'excellent': 1.5,
                'perfect': 1.5,
                'great': 0.8,
                'assist': 0.8,
                'steal': 0.8,
                'block': 0.8,
                'rebound': 0.5,
                'points': 0.3,
                'lead': 0.5,
                'winning': 0.8,
                'clutch': 1.0,
                'hot': 0.8,
                'streak': 0.8,
            },
            # Negative rewards (bad plays)
            'negative': {
                'missed': -0.8,
                'turnover': -1.0,
                'foul': -0.5,
                'bad': -0.8,
                'terrible': -1.2,
                'awful': -1.0,
                'poor': -0.5,
                'lost': -0.5,
                'behind': -0.3,
                'cold': -0.5,
                'brick': -0.8,
                'airball': -1.0,
                'violation': -0.8,
                'technical': -1.0,
                'flagrant': -1.5,
                'ejected': -2.0,
            }
        }
    
    def cleanup(self):
        """Clean up MSS resources."""
        try:
            if hasattr(self, 'sct') and self.sct is not None:
                self.sct.close()
                self.sct = None
        except Exception as e:
            # Ignore cleanup errors
            pass
    
    def analyze_text_for_reward(self, text):
        """
        Analyze OCR text and calculate reward based on detected terms.
        
        Args:
            text (str): The OCR text to analyze
            
        Returns:
            dict: Contains 'reward', 'matched_terms', and 'text_clean'
        """
        if not text or not text.strip():
            return {'reward': 0.0, 'matched_terms': [], 'text_clean': ''}
        
        # Clean and normalize text
        text_clean = text.lower().strip()
        words = re.findall(r'\b\w+\b', text_clean)  # Extract words only
        
        total_reward = 0.0
        matched_terms = []
        
        # Check for positive terms
        for word in words:
            if word in self.reward_terms['positive']:
                reward_value = self.reward_terms['positive'][word]
                total_reward += reward_value
                matched_terms.append(f"+{reward_value} ({word})")
        
        # Check for negative terms
        for word in words:
            if word in self.reward_terms['negative']:
                reward_value = self.reward_terms['negative'][word]
                total_reward += reward_value  # reward_value is already negative
                matched_terms.append(f"{reward_value} ({word})")
        
        return {
            'reward': total_reward,
            'matched_terms': matched_terms,
            'text_clean': ' '.join(words)
        }
    
    def _preprocess_image(self, image):
        """
        Converts an image to grayscale and applies thresholding for better OCR accuracy.
        """
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary_image = cv2.threshold(gray_image, 150, 255, cv2.THRESH_BINARY_INV)
        return binary_image

    def read_text_from_region(self, region_index):
        """
        Captures a specific OCR region, preprocesses it, and extracts text.

        Args:
            region_index (int): The region to read from (1 or 2).

        Returns:
            str: The extracted text, or an empty string if no text is found.
        """
        if region_index == 1:
            region_to_capture = self.region1
        elif region_index == 2:
            region_to_capture = self.region2
        else:
            raise ValueError("region_index must be 1 or 2")

        # Check if MSS is available
        if self.sct is None:
            print("Warning: MSS not available, cannot capture screen")
            return ""

        try:
            # Convert region format from (x, y, width, height) to MSS format
            x, y, width, height = region_to_capture
            monitor = {"top": y, "left": x, "width": width, "height": height}
            
            # Capture the screen region
            screenshot = self.sct.grab(monitor)
            
            # Convert to numpy array (BGR format for OpenCV)
            frame = np.array(screenshot)
            frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
            
        except Exception as e:
            print(f"Warning: Screen capture failed: {e}")
            return ""

        if frame is None:
            return ""

        processed_image = self._preprocess_image(frame)
        
        try:
            custom_config = r'--oem 3 --psm 6'
            text = pytesseract.image_to_string(processed_image, config=custom_config)
            return text.strip().lower()
        except pytesseract.TesseractNotFoundError:
            print("Tesseract Error: Tesseract executable not found. Check the path.")
            return ""
        except Exception as e:
            print(f"An unexpected OCR error occurred: {e}")
            return ""
    
    def read_text_with_reward(self, region_index):
        """
        Captures OCR text and analyzes it for reward calculation.
        
        Args:
            region_index (int): The region to read from (1 or 2).
            
        Returns:
            dict: Contains 'text', 'reward', 'matched_terms', 'text_clean'
        """
        raw_text = self.read_text_from_region(region_index)
        analysis = self.analyze_text_for_reward(raw_text)
        
        return {
            'text': raw_text,
            'reward': analysis['reward'],
            'matched_terms': analysis['matched_terms'],
            'text_clean': analysis['text_clean']
        }

# This block allows you to test the OCR reader directly.
if __name__ == '__main__':
    print("Initializing OCR Reader Test...")
    
    # Try to find the config file in the correct location
    import os
    possible_paths = [
        'F:/YOLO2K/src/config.json',  # Absolute path
        './src/config.json',          # From project root
        './config.json',              # Current directory
        'config.json'                 # Just filename
    ]
    
    CONFIG_PATH = None
    for path in possible_paths:
        if os.path.exists(path):
            CONFIG_PATH = path
            break
    
    if CONFIG_PATH is None:
        print("❌ ERROR: config.json not found in any of these locations:")
        for path in possible_paths:
            print(f"   - {path}")
        print("\nPlease run region_selector.py first to generate the config file.")
        exit(1)
    
    print(f"✅ Found config file at: {CONFIG_PATH}")
    
    # IMPORTANT: You may still need to set this path manually if it's not in your system's PATH
    TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    try:
        reader = OCRReader(config_file_path=CONFIG_PATH, tesseract_path=TESSERACT_PATH)
        print(f"Successfully loaded OCR regions from {CONFIG_PATH}")
        print(f"Region 1 (Feedback): {reader.region1}")
        print(f"Region 2 (Score?): {reader.region2}")
        print("\nStarting capture for 10 seconds...")
        
        import time
        start_time = time.time()
        try:
            while time.time() - start_time < 10:
                # Use new reward-based analysis
                result1 = reader.read_text_with_reward(1)
                result2 = reader.read_text_with_reward(2)
                
                if result1['text']:
                    print(f"📝 Region 1: '{result1['text_clean']}'")
                    if result1['reward'] != 0:
                        print(f"   💰 Reward: {result1['reward']:.1f} {result1['matched_terms']}")
                
                if result2['text']:
                    print(f"📊 Region 2: '{result2['text_clean']}'")
                    if result2['reward'] != 0:
                        print(f"   💰 Reward: {result2['reward']:.1f} {result2['matched_terms']}")
                
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nTest interrupted by user.")
        finally:
            # Ensure proper cleanup
            reader.cleanup()
            print("\nTest finished and resources cleaned up.")

    except FileNotFoundError:
        print(f"ERROR: config.json not found at '{CONFIG_PATH}'")
        print("Please run region_selector.py first to generate the config file.")
    except Exception as e:
        print(f"An error occurred during the test: {e}")
