import cv2
import numpy as np
import pytesseract
import mss
import json
import re
import re

class OCRReader:
    """
    Reads text from specific screen regions defined in a config file.
    Uses MSS for reliable screen capture without COM interface issues.
    Includes reward system based on detected text patterns.
    """
    def __init__(self, config_file_path, tesseract_path):
        """
        Initializes the OCRReader.

        Args:
            config_file_path (str): Path to the JSON config file.
            tesseract_path (str): The full path to the Tesseract OCR executable.
        """
        # Load configuration for OCR regions
        with open(config_file_path, 'r') as f:
            config = json.load(f)
        
        self.region1 = tuple(config.get("ocr_region_1"))
        self.region2 = tuple(config.get("ocr_region_2"))

        if not self.region1 or not self.region2:
            raise ValueError("Config file must contain 'ocr_region_1' and 'ocr_region_2'")

        # Initialize MSS for screen capture
        try:
            self.sct = mss.mss()
        except Exception as e:
            print(f"Warning: MSS initialization failed: {e}")
            self.sct = None
        
        if not tesseract_path:
            raise ValueError("Tesseract executable path cannot be empty.")
        
        # Set the path to the Tesseract executable
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Enhanced NBA 2K25-specific reward dictionary
        self.reward_terms = {
            # High-value positive rewards (excellent plays)
            'excellent': {
                'swish': 2.0,
                'perfect': 2.0,
                'excellent': 1.8,
                'green light': 2.5,
                'green release': 2.5,
                'posterized': 2.0,
                'ankle breaker': 1.8,
                'clutch': 2.0,
                'game winner': 3.0,
                'buzzer beater': 2.5,
                'dunk': 1.5,
                'slam': 1.5,
                'alley oop': 1.8,
            },
            # Good positive rewards (solid plays)
            'good': {
                'scored': 1.2,
                'basket': 1.2,
                'made': 1.0,
                'good': 0.8,
                'nice': 0.8,
                'great': 1.0,
                'assist': 1.0,
                'steal': 1.2,
                'block': 1.2,
                'rebound': 0.6,
                'defensive': 0.8,
                'hustle': 0.8,
                'smart': 0.8,
                'open': 0.6,
                'wide open': 0.8,
                'hot': 1.0,
                'streak': 1.0,
                'momentum': 0.8,
                'lead': 0.6,
                'winning': 1.0,
                'points': 0.4,
                'field goal': 1.0,
                'three pointer': 1.2,
                'free throw': 0.8,
                'and one': 1.5,
            },
            # Moderate negative rewards (poor plays)
            'poor': {
                'missed': -1.0,
                'miss': -1.0,
                'bad': -1.0,
                'poor': -0.8,
                'contested': -0.6,
                'heavily contested': -1.2,
                'smothered': -1.5,
                'late': -0.8,
                'early': -0.8,
                'cold': -0.8,
                'behind': -0.4,
                'losing': -0.8,
                'deficit': -0.6,
            },
            # High-penalty negative rewards (terrible plays)
            'terrible': {
                'turnover': -1.5,
                'stolen': -1.5,
                'intercepted': -1.5,
                'brick': -1.2,
                'airball': -1.5,
                'terrible': -1.8,
                'awful': -1.5,
                'foul': -1.0,
                'flagrant': -2.5,
                'technical': -2.0,
                'ejected': -3.0,
                'violation': -1.2,
                'shot clock': -1.8,
                'backcourt': -1.5,
                'travel': -1.0,
                'double dribble': -1.2,
                'offensive foul': -1.5,
                'charge': -1.2,
                'out of bounds': -1.0,
            }
        }
    
    def cleanup(self):
        """Clean up MSS resources."""
        try:
            if hasattr(self, 'sct') and self.sct is not None:
                self.sct.close()
                self.sct = None
        except Exception as e:
            # Ignore cleanup errors
            pass
    
    def analyze_text_for_reward(self, text):
        """
        Enhanced reward analysis with phrase matching and context awareness.

        Args:
            text (str): The OCR text to analyze

        Returns:
            dict: Contains 'reward', 'matched_terms', 'text_clean', and 'confidence'
        """
        if not text or not text.strip():
            return {'reward': 0.0, 'matched_terms': [], 'text_clean': '', 'confidence': 0.0}

        # Clean and normalize text
        text_clean = text.lower().strip()

        # Extract both individual words and phrases
        words = re.findall(r'\b\w+\b', text_clean)
        phrases = [text_clean]  # Also check full text for multi-word phrases

        total_reward = 0.0
        matched_terms = []
        confidence_score = 0.0

        # Check all reward categories
        for category_name, category_terms in self.reward_terms.items():
            for term, reward_value in category_terms.items():
                # Check if term appears in text (supports multi-word terms)
                if term in text_clean:
                    total_reward += reward_value
                    matched_terms.append(f"{reward_value:+.1f} ({term})")
                    confidence_score += abs(reward_value) * 0.1  # Higher rewards = higher confidence

        # Bonus/penalty for multiple positive/negative events
        positive_count = sum(1 for term in matched_terms if '+' in term)
        negative_count = len(matched_terms) - positive_count

        # Combo bonuses
        if positive_count >= 2:
            combo_bonus = 0.5 * positive_count
            total_reward += combo_bonus
            matched_terms.append(f"+{combo_bonus:.1f} (combo bonus)")

        # Multiple mistakes penalty
        if negative_count >= 2:
            mistake_penalty = -0.3 * negative_count
            total_reward += mistake_penalty
            matched_terms.append(f"{mistake_penalty:.1f} (mistake penalty)")

        return {
            'reward': total_reward,
            'matched_terms': matched_terms,
            'text_clean': ' '.join(words),
            'confidence': min(confidence_score, 10.0)  # Cap confidence at 10
        }
    
    def _preprocess_image(self, image, method='adaptive'):
        """
        Enhanced image preprocessing with multiple methods for better OCR accuracy.

        Args:
            image: Input image
            method: Preprocessing method ('adaptive', 'otsu', 'simple', 'enhanced')
        """
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        if method == 'adaptive':
            # Adaptive thresholding works better with varying lighting
            processed = cv2.adaptiveThreshold(
                gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )
        elif method == 'otsu':
            # Otsu's method automatically finds optimal threshold
            _, processed = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif method == 'enhanced':
            # Enhanced preprocessing with noise reduction
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray_image, (3, 3), 0)
            # Apply adaptive threshold
            processed = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )
            # Apply morphological operations to clean up
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
        else:  # 'simple'
            # Simple binary threshold (original method)
            _, processed = cv2.threshold(gray_image, 150, 255, cv2.THRESH_BINARY)

        return processed

    def read_text_from_region(self, region_index, save_debug_image=False):
        """
        Enhanced OCR with multiple preprocessing methods and confidence scoring.

        Args:
            region_index (int): The region to read from (1 or 2).
            save_debug_image (bool): Save processed images for debugging.

        Returns:
            str: The extracted text, or an empty string if no text is found.
        """
        if region_index == 1:
            region_to_capture = self.region1
        elif region_index == 2:
            region_to_capture = self.region2
        else:
            raise ValueError("region_index must be 1 or 2")

        # Check if MSS is available
        if self.sct is None:
            print("Warning: MSS not available, cannot capture screen")
            return ""

        try:
            # Convert region format from (x, y, width, height) to MSS format
            x, y, width, height = region_to_capture
            monitor = {"top": y, "left": x, "width": width, "height": height}

            # Capture the screen region
            screenshot = self.sct.grab(monitor)

            # Convert to numpy array (BGR format for OpenCV)
            frame = np.array(screenshot)
            frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

        except Exception as e:
            print(f"Warning: Screen capture failed: {e}")
            return ""

        if frame is None:
            return ""

        # Try multiple preprocessing methods and pick the best result
        methods = ['enhanced', 'adaptive', 'otsu', 'simple']
        best_text = ""
        best_confidence = 0

        for method in methods:
            try:
                processed_image = self._preprocess_image(frame, method)

                # Save debug images if requested
                if save_debug_image:
                    debug_path = f"debug_ocr_region{region_index}_{method}.png"
                    cv2.imwrite(debug_path, processed_image)

                # Try different PSM modes for better text detection
                psm_modes = [6, 8, 7, 13]  # Different page segmentation modes

                for psm in psm_modes:
                    custom_config = f'--oem 3 --psm {psm} -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .,!?-'

                    # Get text with confidence data
                    data = pytesseract.image_to_data(processed_image, config=custom_config, output_type=pytesseract.Output.DICT)

                    # Calculate average confidence for detected text
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    if confidences:
                        avg_confidence = sum(confidences) / len(confidences)
                        text = ' '.join([data['text'][i] for i in range(len(data['text'])) if int(data['conf'][i]) > 30])

                        if text.strip() and avg_confidence > best_confidence:
                            best_text = text.strip()
                            best_confidence = avg_confidence

            except Exception as e:
                continue  # Try next method

        # Only return text if confidence is reasonable
        if best_confidence > 40:  # Minimum confidence threshold
            return best_text.lower()
        else:
            return ""
    
    def read_text_with_reward(self, region_index):
        """
        Captures OCR text and analyzes it for reward calculation.
        
        Args:
            region_index (int): The region to read from (1 or 2).
            
        Returns:
            dict: Contains 'text', 'reward', 'matched_terms', 'text_clean'
        """
        raw_text = self.read_text_from_region(region_index)
        analysis = self.analyze_text_for_reward(raw_text)
        
        return {
            'text': raw_text,
            'reward': analysis['reward'],
            'matched_terms': analysis['matched_terms'],
            'text_clean': analysis['text_clean']
        }

# This block allows you to test the OCR reader directly.
if __name__ == '__main__':
    print("Initializing OCR Reader Test...")
    
    # Try to find the config file in the correct location
    import os
    possible_paths = [
        'F:/YOLO2K/src/config.json',  # Absolute path
        './src/config.json',          # From project root
        './config.json',              # Current directory
        'config.json'                 # Just filename
    ]
    
    CONFIG_PATH = None
    for path in possible_paths:
        if os.path.exists(path):
            CONFIG_PATH = path
            break
    
    if CONFIG_PATH is None:
        print("❌ ERROR: config.json not found in any of these locations:")
        for path in possible_paths:
            print(f"   - {path}")
        print("\nPlease run region_selector.py first to generate the config file.")
        exit(1)
    
    print(f"✅ Found config file at: {CONFIG_PATH}")
    
    # IMPORTANT: You may still need to set this path manually if it's not in your system's PATH
    TESSERACT_PATH = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    try:
        reader = OCRReader(config_file_path=CONFIG_PATH, tesseract_path=TESSERACT_PATH)
        print(f"Successfully loaded OCR regions from {CONFIG_PATH}")
        print(f"Region 1 (Feedback): {reader.region1}")
        print(f"Region 2 (Score?): {reader.region2}")
        print("\nStarting capture for 10 seconds...")
        
        import time
        start_time = time.time()
        try:
            while time.time() - start_time < 10:
                # Use new reward-based analysis
                result1 = reader.read_text_with_reward(1)
                result2 = reader.read_text_with_reward(2)
                
                if result1['text']:
                    print(f"📝 Region 1: '{result1['text_clean']}'")
                    if result1['reward'] != 0:
                        print(f"   💰 Reward: {result1['reward']:.1f} {result1['matched_terms']}")
                
                if result2['text']:
                    print(f"📊 Region 2: '{result2['text_clean']}'")
                    if result2['reward'] != 0:
                        print(f"   💰 Reward: {result2['reward']:.1f} {result2['matched_terms']}")
                
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nTest interrupted by user.")
        finally:
            # Ensure proper cleanup
            reader.cleanup()
            print("\nTest finished and resources cleaned up.")

    except FileNotFoundError:
        print(f"ERROR: config.json not found at '{CONFIG_PATH}'")
        print("Please run region_selector.py first to generate the config file.")
    except Exception as e:
        print(f"An error occurred during the test: {e}")
