# NBA 2K25 AI Bot Setup Guide

## 🎯 Quick Start

1. **Run the setup script:**
   ```bash
   python setup_nba2k25.py
   ```

2. **Set up screen regions (choose one):**
   ```bash
   # Live region selector (recommended for NBA 2K25)
   python src/region_selector_live.py
   
   # OR standard region selector
   python src/region_selector.py
   ```

3. **Test the bot:**
   ```bash
   python src/main.py
   ```

4. **Start training:**
   ```bash
   python src/train.py
   ```

## 🏀 NBA 2K25 Specific Configuration

### Screen Regions for NBA 2K25

#### Region Selector Options:

**🔴 Live Region Selector (Recommended):**
- Real-time screen updates while selecting regions
- NBA 2K25 specific guidance and tips
- Live preview of selected areas
- Better for dynamic games like NBA 2K25

**📷 Standard Region Selector:**
- Single screenshot approach
- Manual refresh when needed
- Lighter on system resources

When running either region selector, you'll need to select these regions:

#### 1. Main Capture Region (YOLO Detection)
- **Purpose:** Where YOLO detects players, ball, court elements
- **Recommended:** The main game court area (exclude UI elements)
- **Typical coordinates:** `(100, 100, 1820, 980)` for 1920x1080 screen
- **What to include:** Players, basketball, court, referee
- **What to exclude:** Score overlay, menus, advertisements

#### 2. OCR Region 1 (Primary Feedback)
- **Purpose:** Game feedback text (shot feedback, turnovers, etc.)
- **Location:** Usually top-center or bottom-center of screen
- **Typical coordinates:** `(760, 100, 1160, 200)` for 1920x1080
- **Look for text like:**
  - "Good Shot Selection"
  - "Turnover"
  - "Assist"
  - "Steal"
  - "Block"

#### 3. OCR Region 2 (Secondary Info)
- **Purpose:** Score, time, player stats
- **Location:** Usually top corners of screen
- **Typical coordinates:** `(1520, 50, 1870, 150)` for 1920x1080
- **Look for:**
  - Score updates
  - Time remaining
  - Player names
  - Fouls

### AMD RX 580 Optimization

Your RX 580 is automatically optimized with:

- **YOLO Detection:** Runs on GPU using OpenCL
- **Neural Network:** Uses CUDA/ROCm if available
- **Memory Management:** Optimized for 8GB VRAM
- **Mixed Precision:** Enabled for faster training

### NBA 2K25 Reward Keywords

Edit these in `src/train.py` to match NBA 2K25's feedback:

```python
self.reward_keywords = {
    # Positive rewards
    "excellent shot": 10.0,
    "good shot selection": 8.0,
    "green light": 10.0,
    "swish": 8.0,
    "perfect release": 9.0,
    "assist": 7.0,
    "steal": 8.0,
    "block": 8.0,
    "good defense": 5.0,
    "rebound": 4.0,
    
    # Negative rewards
    "turnover": -10.0,
    "bad shot selection": -8.0,
    "shot clock violation": -12.0,
    "heavily contested": -6.0,
    "blocked shot": -7.0,
    "foul": -5.0,
    "out of bounds": -4.0,
    "travel": -6.0,
    "double dribble": -6.0
}
```

## 🎮 Game Settings for Best Results

### NBA 2K25 Recommended Settings:

1. **Display Mode:** Windowed or Borderless Windowed
2. **Resolution:** 1920x1080 (adjust regions accordingly for other resolutions)
3. **Camera:** 2K or Broadcast (consistent view)
4. **Game Speed:** Normal (not simulation)
5. **Difficulty:** Start with Pro, increase as AI improves
6. **Feedback:** Enable all text feedback
7. **Subtitles:** On (helps OCR read feedback)

### Controller Setup:

The bot uses virtual controller passthrough:
- Your physical controller works normally
- Bot can override inputs when making decisions
- Install ViGEmBus driver (included with vgamepad)

## 🔧 Troubleshooting

### Common Issues:

#### "Config file not found"
```bash
python src/region_selector.py
```

#### "Tesseract not found"
- Download from: https://github.com/UB-Mannheim/tesseract/wiki
- Install to default location: `C:\Program Files\Tesseract-OCR\`

#### "YOLO weights not found"
```bash
python download_models.py
```

#### "No GPU detected"
- Install AMD ROCm for PyTorch GPU support
- Or use CPU mode (slower but works)

#### OCR not reading text
- Adjust OCR regions to exactly match text areas
- Check Tesseract path in code
- Ensure text is clearly visible (not overlapped)

### Performance Tips:

1. **Close unnecessary programs** to free GPU memory
2. **Lower game graphics** if detection is slow
3. **Adjust confidence thresholds** in detection settings
4. **Monitor GPU usage** during training

## 📊 Training Tips

### Starting Training:

1. **Begin with simple scenarios** (practice mode)
2. **Monitor reward feedback** in console
3. **Adjust reward values** based on game feedback
4. **Save models frequently** (every 10 episodes)
5. **Test periodically** with main.py

### Expected Training Time:

- **Initial learning:** 100-500 episodes
- **Basic competency:** 1000-2000 episodes  
- **Advanced play:** 5000+ episodes
- **Training time:** 2-8 hours depending on episode length

### Monitoring Progress:

Watch for:
- Increasing average rewards
- More consistent actions
- Better shot selection
- Fewer turnovers

## 🚀 Advanced Configuration

### Custom Actions:

Add more actions in `decision_ai.py`:

```python
class Action(Enum):
    DO_NOTHING = 0
    MOVE_FORWARD = 1
    MOVE_BACKWARD = 2
    MOVE_LEFT = 3
    MOVE_RIGHT = 4
    PASS = 5
    SHOOT = 6
    SPRINT = 7
    STEAL = 8        # New action
    CALL_PLAY = 9    # New action
    TIMEOUT = 10     # New action
```

### Multiple OCR Regions:

You can add more OCR regions by modifying the config file and OCR reader.

### Custom Reward Functions:

Create complex reward calculations based on game state, score differential, time remaining, etc.

## 📝 Next Steps

1. **Run the setup script** to get everything configured
2. **Test with a simple game mode** first
3. **Gradually increase complexity** as the AI learns
4. **Experiment with different reward structures**
5. **Share your results** and improvements!

Good luck training your NBA 2K25 AI! 🏀🤖